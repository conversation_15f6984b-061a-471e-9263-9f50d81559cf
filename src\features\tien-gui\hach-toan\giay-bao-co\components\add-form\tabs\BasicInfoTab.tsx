import { accountSearchColumns, taiKhoanNganHangSearchColumns, MA_CHUNG_TU, QUERY_KEYS } from '@/constants';
import { SearchField, FormField, CurrencyInput, DocumentNumberField } from '@/components/custom/arito';
import { FormFieldState, FormFieldActions } from '../../../hooks';
import { TaiKhoan, TaiKhoanNganHang } from '@/types/schemas';
import { Label } from '@/components/ui/label';
import { FormMode } from '@/types/form';

interface Props {
  formMode: FormMode;
  formState: {
    state: FormFieldState;
    actions: FormFieldActions;
  };
}

export const BasicInfoTab = ({ formMode, formState: { state, actions } }: Props) => {
  return (
    <div className='space-y-2 p-4'>
      <div className='flex flex-col gap-6 lg:flex-row lg:gap-8'>
        <div className='flex-1 space-y-2'>
          <div className='flex items-center gap-2'>
            <Label className='w-32 min-w-32 text-left'><PERSON><PERSON><PERSON> chứng từ</Label>
            <div className='w-[250px]'>
              <FormField
                type='select'
                options={[
                  { value: '1', label: '1. Theo hóa đơn' },
                  { value: '2', label: '2. Theo đối tượng' },
                  { value: '3', label: '3. Thu khác' }
                ]}
                name='ma_ngv'
                disabled={formMode === 'view'}
              />
            </div>
          </div>

          {/* Address field */}
          <div className='flex items-center gap-2'>
            <Label className='w-32 min-w-32 text-left'>Địa chỉ</Label>
            <div className='flex-1'>
              <FormField type='text' name='dia_chi' disabled={formMode === 'view'} />
            </div>
          </div>

          {/* Payer field */}
          <div className='flex items-center gap-2'>
            <Label className='w-32 min-w-32 text-left'>Người nộp tiền</Label>
            <div className='flex-1'>
              <FormField type='text' name='ong_ba' disabled={formMode === 'view'} />
            </div>
          </div>

          {/* Description field */}
          <div className='flex items-center gap-2'>
            <Label className='w-32 min-w-32 text-left'>Diễn giải</Label>
            <div className='flex-1'>
              <FormField name='dien_giai' type='text' disabled={formMode === 'view'} />
            </div>
          </div>

          <div className='flex flex-col gap-3 lg:flex-row lg:gap-4'>
            <div className='flex items-center gap-2 lg:flex-1'>
              <Label className='w-32 min-w-32 text-left lg:w-32 lg:min-w-32'>Ngân hàng</Label>
              <div className='flex-1'>
                <SearchField<TaiKhoanNganHang>
                  type='text'
                  value={state.taiKhoanNganHang?.account_code || ''}
                  searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN_NGAN_HANG}`}
                  searchColumns={taiKhoanNganHangSearchColumns}
                  columnDisplay='account_code'
                  displayRelatedField='name'
                  dialogTitle='Danh mục tài khoản ngân hàng'
                  onRowSelection={actions.setTaiKhoanNganHang}
                  relatedFieldValue={state.taiKhoanNganHang?.name || ''}
                  disabled={formMode === 'view'}
                />
              </div>
            </div>
            <div className='flex items-center gap-2 lg:flex-1'>
              <Label className='w-32 min-w-32 text-left lg:w-32 lg:min-w-32'>Tài khoản nợ</Label>
              <div className='flex-1'>
                <SearchField<TaiKhoan>
                  type='text'
                  displayRelatedField='name'
                  columnDisplay='code'
                  className='w-[180px]'
                  searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
                  searchColumns={accountSearchColumns}
                  dialogTitle='Danh mục tài khoản'
                  value={state.taiKhoan?.code || ''}
                  relatedFieldValue={state.taiKhoan?.name || ''}
                  onRowSelection={actions.setTaiKhoan}
                  disabled={formMode === 'view'}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Right column - Secondary form fields */}
        <div className='w-full space-y-3 lg:w-80'>
          {/* Document number field */}
          <DocumentNumberField
            ma_ct={MA_CHUNG_TU.TIEN_GUI.GIAY_BAO_CO}
            quyenChungTu={state.quyenChungTu}
            onQuyenChungTuChange={actions.setQuyenChungTu}
            soChungTu={state.soChungTu}
            onSoChungTuChange={actions.setSoChungTu}
            disabled={formMode === 'view'}
            classNameSearchField='w-full'
          />

          {/* Document date field */}
          <div className='flex items-center gap-2'>
            <Label className='w-32 min-w-32 text-left'>Ngày chứng từ</Label>
            <div className='flex-1'>
              <FormField type='date' name='ngay_ct' disabled={formMode === 'view'} />
            </div>
          </div>

          {/* Creation date field */}
          <div className='flex items-center gap-2'>
            <Label className='w-32 min-w-32 text-left'>Ngày lập chứng từ</Label>
            <div className='flex-1'>
              <FormField name='ngay_lct' type='date' disabled={formMode === 'view'} />
            </div>
          </div>

          {/* Foreign currency and exchange rate - responsive row */}
          <CurrencyInput formMode={formMode} classNameInput='w-full' />

          {/* Status field */}
          <div className='flex items-center gap-2'>
            <Label className='w-32 min-w-32 text-left'>Trạng thái</Label>
            <div className='flex-1'>
              <FormField
                name='status'
                type='select'
                disabled={formMode === 'view'}
                options={[
                  { value: '0', label: 'Chưa duyệt' },
                  { value: '1', label: 'Đã duyệt' },
                  { value: '2', label: 'Đã ghi sổ' }
                ]}
              />
            </div>
          </div>

          {/* Data received checkbox */}
          <div className='flex items-center gap-2'>
            <FormField label='Dữ liệu được nhận' name='transfer_yn' type='checkbox' disabled={formMode === 'view'} />
          </div>
        </div>
      </div>
    </div>
  );
};
