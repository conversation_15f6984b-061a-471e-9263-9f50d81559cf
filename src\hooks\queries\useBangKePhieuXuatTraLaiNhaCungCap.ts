import { useState, useCallback } from 'react';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

interface BangKePhieuXuatTraLaiItem {
  id: string;
  don_vi: string;
  ngay_ctu: string;
  so_ct: string;
  ma_kh: string;
  ten_kh: string;
  dien_giai: string;
  ma_vt: string;
  ten_vt: string;
  dvt: string;
  so_luong: number;
  gia_nt: number;
  tien_nt: number;
  gia_vnd: number;
  tien_vnd: number;
  tk_co: string;
  tk_no: string;
  ma_kho: string;
  ma_bp: string;
  ma_vv: string;
  ma_hd: string;
  ma_dtt: string;
  ma_ku: string;
  ma_phi: string;
  ma_sp: string;
  ma_lsx: string;
  ma_cp0: string;
  ma_ctu: string;
}

interface BangKePhieuXuatTraLaiResponse {
  results: BangKePhieuXuatTraLaiItem[];
  count: number;
}

interface BangKePhieuXuatTraLaiSearchParams {
  ngay_ct1?: string;
  ngay_ct2?: string;
  so_ct1?: string;
  so_ct2?: string;
  ma_kho?: string;
  tk_vt?: string;
  tk_dt?: string;
  ma_kh?: string;
  ma_nt?: string;
  dien_giai?: string;
  mau_bc?: number;
  ma_bp?: string;
  ma_vv?: string;
  ma_hd?: string;
  ma_dtt?: string;
  ma_ku?: string;
  ma_phi?: string;
  ma_sp?: string;
  ma_lsx?: string;
  ma_cp0?: string;
  report_filtering?: string;
  data_analysis_struct?: string;
}

interface UseBangKePhieuXuatTraLaiReturn {
  data: BangKePhieuXuatTraLaiItem[];
  isLoading: boolean;
  error: string | null;
  fetchData: (searchParams: any) => Promise<void>;
  refreshData: (searchParams: any) => Promise<void>;
  postData: (searchParams: any) => Promise<void>;
}

/**
 * Transform search form values to API parameters
 * Converts form data to the format expected by the API endpoint
 */
const transformSearchParams = (searchParams: any): BangKePhieuXuatTraLaiSearchParams => {
  return {
    // Convert dates to string format (YYYY-MM-DD)
    ngay_ct1: searchParams.ngay_ct1 ? searchParams.ngay_ct1.toString() : undefined,
    ngay_ct2: searchParams.ngay_ct2 ? searchParams.ngay_ct2.toString() : undefined,

    // Document number range
    so_ct1: searchParams.so_ct1 || undefined,
    so_ct2: searchParams.so_ct2 || undefined,

    // General tab fields
    ma_kho: searchParams.ma_kho || undefined,
    tk_vt: searchParams.tk_vt || undefined,
    tk_dt: searchParams.tk_dt || undefined,
    ma_kh: searchParams.ma_kh || undefined,
    ma_nt: searchParams.ma_nt || undefined,
    dien_giai: searchParams.dien_giai || undefined,
    mau_bc: searchParams.mau_bc,

    // Filter tab fields
    ma_bp: searchParams.ma_bp || undefined,
    ma_vv: searchParams.ma_vv || undefined,
    ma_hd: searchParams.ma_hd || undefined,
    ma_dtt: searchParams.ma_dtt || undefined,
    ma_ku: searchParams.ma_ku || undefined,
    ma_phi: searchParams.ma_phi || undefined,
    ma_sp: searchParams.ma_sp || undefined,
    ma_lsx: searchParams.ma_lsx || undefined,
    ma_cp0: searchParams.ma_cp0 || undefined,

    report_filtering: searchParams.report_filtering === '0' ? '' : searchParams.report_filtering || '',
    data_analysis_struct: searchParams.data_analysis_struct === '0' ? '' : searchParams.data_analysis_struct || ''
  };
};

/**
 * Custom hook for managing Bang Ke Phieu Xuat Tra Lai Nha Cung Cap data
 *
 * This hook provides functionality to fetch return voucher report data from the API endpoint.
 * It uses POST method to send search parameters and retrieve filtered results.
 */
export const useBangKePhieuXuatTraLaiNhaCungCap = (): UseBangKePhieuXuatTraLaiReturn => {
  const [data, setData] = useState<BangKePhieuXuatTraLaiItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const { entity } = useAuth();

  const fetchData = useCallback(
    async (searchParams: any) => {
      setIsLoading(true);
      setError(null);

      try {
        if (!entity?.slug) {
          setError('Entity not found');
          return;
        }

        const apiParams = transformSearchParams(searchParams);
        const endpoint = `/entities/${entity.slug}/erp/mua-hang/bao-cao-mua-hang/bang-ke-phieu-xuat-tra-lai-nha-cung-cap/`;

        const response = await api.post<BangKePhieuXuatTraLaiResponse>(endpoint, apiParams);

        if (response.data && response.data.results) {
          setData(response.data.results);
        } else if (response.data && Array.isArray(response.data)) {
          setData(response.data as BangKePhieuXuatTraLaiItem[]);
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while fetching data';
        setError(errorMessage);
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [entity?.slug]
  );

  const refreshData = useCallback(
    async (searchParams: any) => {
      await fetchData(searchParams);
    },
    [fetchData]
  );

  const postData = useCallback(
    async (searchParams: any) => {
      setIsLoading(true);
      setError(null);

      try {
        if (!entity?.slug) {
          setError('Entity not found');
          return;
        }

        const apiParams = transformSearchParams(searchParams);
        const endpoint = `/entities/${entity.slug}/erp/mua-hang/bao-cao-mua-hang/bang-ke-phieu-xuat-tra-lai-nha-cung-cap/`;

        const response = await api.post<BangKePhieuXuatTraLaiResponse>(endpoint, apiParams);

        if (response.data && response.data.results) {
          setData(response.data.results);
        } else if (response.data && Array.isArray(response.data)) {
          setData(response.data as BangKePhieuXuatTraLaiItem[]);
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while posting data';
        setError(errorMessage);
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [entity?.slug]
  );

  return {
    data,
    isLoading,
    error,
    fetchData,
    refreshData,
    postData
  };
};
