import { useState, useEffect, useCallback } from 'react';
import type { ApiResponse } from '@/types/api.type';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

// Generic interfaces for reusable data hook
interface UseDataConfig {
  endpoint: string;
  searchData?: any;
  pageSize?: number;
  // Whether to fetch the initial list automatically on mount/effect. Default: true
  initialFetch?: boolean;
}

interface UseDataReturn<TData, TInput> {
  data: TData[];
  detail: any[] | null;
  isLoading: boolean;
  isDetailLoading: boolean;
  error: string | null;
  totalItems: number;
  currentPage: number;
  handlePageChange: (page: number) => Promise<void>;
  addItem: (data: TInput) => Promise<TData>;
  fetchDetail: (uuid: string) => Promise<any[]>;
  updateItem: (uuid: string, data: TInput) => Promise<TData>;
  deleteItem: (uuid: string) => Promise<void>;
  refreshData: () => Promise<void>;
}

/**
 * Generic reusable data hook for CRUD operations
 * @param config - Configuration object with endpoint and optional queryKey
 * @param initialList - Initial data list (optional)
 * @param searchData - Search parameters (optional)
 * @returns Hook return object with data and CRUD operations
 */
export const useCRUD = <TData extends { uuid: string }, TInput = Partial<TData>>(
  config: UseDataConfig,
  initialList?: TData[]
): UseDataReturn<TData, TInput> => {
  const [data, setData] = useState<TData[]>(initialList || []);
  const [detail, setDetail] = useState<any[] | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(config.initialFetch !== false);
  const [isDetailLoading, setIsDetailLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(0);

  const { entity } = useAuth();
  const { endpoint, searchData, pageSize = 10 } = config;

  const fetchData = useCallback(
    async (paginationParams?: { page?: number; pageSize?: number }) => {
      if (!entity?.slug) return;

      setIsLoading(true);
      try {
        const url = `/entities/${entity.slug}/erp/${endpoint}`;
        const requestParams: any = { ...searchData };

        // Add pagination parameters
        if (paginationParams?.page !== undefined) {
          requestParams.page = paginationParams.page + 1; // API expects 1-based page numbers
        }
        if (paginationParams?.pageSize !== undefined) {
          requestParams.page_size = paginationParams.pageSize;
        }

        const response = await api.get<ApiResponse<TData>>(url, { params: requestParams });
        setData(response.data.results);
        setTotalItems(response.data.count || response.data.results?.length || 0);
        setError(null);
      } catch (error: any) {
        setError(error.message || 'Có lỗi xảy ra khi tải dữ liệu');
        setData([]);
        setTotalItems(0);
      } finally {
        setIsLoading(false);
      }
    },
    [entity?.slug, endpoint, searchData]
  );

  const fetchDetail = async (uuid: string): Promise<any[]> => {
    if (!entity?.slug) return [];
    if (!uuid) return [];

    setIsDetailLoading(true);
    try {
      const response = await api.get(`/entities/${entity.slug}/erp/${endpoint}/${uuid}/chi-tiet/`);
      setDetail(response.data.results);
      return response.data.results;
    } catch (error) {
      setDetail([]);
      return [];
    } finally {
      setIsDetailLoading(false);
    }
  };

  const addItem = async (itemData: TInput): Promise<TData> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    setIsLoading(true);
    try {
      const response = await api.post<TData>(`/entities/${entity.slug}/erp/${endpoint}/`, itemData, {
        params: searchData
      });
      setData(prev => [...prev, response.data]);
      setError(null);
      return response.data;
    } catch (error: any) {
      setError(error.message || 'Có lỗi xảy ra khi thêm dữ liệu');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateItem = async (uuid: string, itemData: TInput): Promise<TData> => {
    if (!entity?.slug) throw new Error('Entity slug is required');

    setIsLoading(true);
    try {
      const response = await api.put<TData>(`/entities/${entity.slug}/erp/${endpoint}/${uuid}/`, itemData, {
        params: searchData
      });
      setData(prev => prev.map(item => (item.uuid === uuid ? response.data : item)));
      setError(null);
      return response.data;
    } catch (error: any) {
      setError(error.message || 'Có lỗi xảy ra khi cập nhật dữ liệu');
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteItem = async (uuid: string | undefined): Promise<void> => {
    if (!entity?.slug) throw new Error('Entity slug is required');
    if (!uuid) return;

    setIsLoading(true);
    try {
      await api.delete(`/entities/${entity.slug}/erp/${endpoint}/${uuid}/`);
      setData(prev => prev.filter(item => item.uuid !== uuid));
      setError(null);
    } catch (error: any) {
      setError(error.message || 'Có lỗi xảy ra khi xóa dữ liệu');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle page change for pagination
  const handlePageChange = useCallback(
    async (page: number) => {
      setCurrentPage(page);
      await fetchData({ page, pageSize });
    },
    [fetchData, pageSize]
  );

  // Refresh function that maintains current pagination
  const refreshData = useCallback(async (): Promise<void> => {
    await fetchData({ page: currentPage, pageSize });
  }, [fetchData, currentPage, pageSize]);

  useEffect(() => {
    if (config.initialFetch !== false) {
      fetchData({ page: currentPage, pageSize });
    }
  }, [entity?.slug, searchData, fetchData, currentPage, pageSize, config.initialFetch]);

  return {
    data,
    detail,
    error,
    isLoading,
    isDetailLoading,
    totalItems,
    currentPage,
    handlePageChange,
    addItem,
    fetchDetail,
    updateItem,
    deleteItem,
    refreshData
  };
};
