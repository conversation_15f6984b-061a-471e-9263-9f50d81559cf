## GUIDE: <PERSON><PERSON><PERSON> Thu từ nhiều hóa đơn (MultipleForms)

Mục tiêu: <PERSON><PERSON> truy cập trang Phiếu thu với URL có tham số hdbh hoặc hdbdv, hệ thống tự động:
- Bỏ fetch danh sách ban đầu (không tải bảng dữ liệu chính)
- Ẩn trang chính và mở luồng tạo phiếu thu theo từng hóa đơn (MultipleForms)
- Sau mỗi lần tạo phiếu: hiển thị toast "Tạo phiếu thu thành công"
- <PERSON><PERSON> khi tạo xong tất cả phiếu: tự động quay lại trang trước (router.back)


### 1) Tổng quan kiến trúc
- useMultipleForms (hooks/useMultipleForms.ts): đóng gói toàn bộ logic MultipleForms (đọc params, fetch dữ liệu hóa đơn, state form hiện tại, initialData, submit/close)
- useInvoiceParamsPresence: helper đ<PERSON> x<PERSON>c định có params hdbh/hdbdv hay không
- useDataTables: nhận options.initialFetch để điều kiện hóa việc fetch danh sách ban đầu (dựa theo hasInvoiceParams)
- Trang index.tsx: sử dụng các hook trên để điều phối UI


### 2) Tham số URL được hỗ trợ
- hdbh: danh sách UUID hóa đơn bán hàng, phân tách bằng dấu phẩy
- hdbdv: danh sách UUID hóa đơn bán dịch vụ, phân tách bằng dấu phẩy

Ví dụ:
- /tien-mat/hach-toan/phieu-thu?hdbh=uuid1,uuid2
- /tien-mat/hach-toan/phieu-thu?hdbdv=uuidA


### 3) Hook useMultipleForms
Công dụng:
- Phát hiện và xử lý params hdbh/hdbdv
- Fetch dữ liệu hóa đơn theo UUID
- Quản lý state MultipleForms (showMultipleForms, invoiceData, currentFormIndex)
- Tạo initialData để truyền vào PhieuThuForm từ dữ liệu hóa đơn (transformDataFromHoaDon) và mặc định tk_data (tài khoản 1111)
- Xử lý submit: tạo phiếu thu, hiện toast, chuyển form tiếp theo hoặc back khi xong

API:
- Input: { addPhieuThu } (hàm tạo phiếu thu)
- Output:
  - hasInvoiceParams: boolean
  - showMultipleForms: boolean
  - invoiceData: any[]
  - currentFormIndex: number
  - initialData: dữ liệu khởi tạo form (đã merge tk_data nếu có)
  - handleMultipleFormSubmit(data): submit tạo phiếu
  - handleCloseMultipleForms(): đóng luồng MultipleForms

Lưu ý:
- Khi submit thành công: toast("Tạo phiếu thu thành công") và nếu là phiếu cuối cùng thì router.back()


### 4) Bỏ fetch dữ liệu ban đầu khi có params
- useCRUD hỗ trợ config.initialFetch (mặc định true)
- useDataTables truyền initialFetch từ options.initialFetch
- Tại trang index.tsx:
  - hasInvoiceParams = useInvoiceParamsPresence()
  - Gọi useDataTables(handleViewClick, { initialFetch: !hasInvoiceParams })
    - Nếu có params => initialFetch = false => không fetch list, ẩn trang chính


### 5) Tích hợp ở trang index.tsx (rút gọn)
- Tạo biến hasInvoiceParams từ useInvoiceParamsPresence()
- Khởi tạo data tables với initialFetch theo hasInvoiceParams
- Gọi useMultipleForms({ addPhieuThu }) lấy state và handlers
- Render:
  - Nếu showMultipleForms && invoiceData.length > 0 => hiển thị <PhieuThuForm formMode="add" initialData={initialData} onSubmit={handleMultipleFormSubmit} onClose={handleCloseMultipleForms} />
  - Nếu không, render trang chính như cũ


### 6) Xử lý dữ liệu initialData
- transformDataFromHoaDon(hoaDonData) => dựng dữ liệu cơ bản + child_data
- Tự động gán tk_data mặc định khi đã load xong danh mục tài khoản (prefix 1111)


### 7) Endpoint & fetch hóa đơn
- Hóa đơn bán hàng: QUERY_KEYS.HOA_DON_BAN_HANG
- Hóa đơn bán dịch vụ: QUERY_KEYS.HOA_DON_BAN_DICH_VU
- useMultipleForms tự fetch dựa vào entity.slug và UUID trong params


### 8) Toast & Toaster
- Dùng toast từ hooks/use-toast
- Toaster đã được render trong layout (src/app/layout.tsx). Không cần cài thêm


### 9) Luồng hoạt động
1. Người dùng truy cập trang kèm hdbh/hdbdv => hasInvoiceParams = true
2. useDataTables initialFetch=false => không load danh sách
3. useMultipleForms đọc params, fetch dữ liệu từng hóa đơn, bật showMultipleForms
4. Render PhieuThuForm (mode add) với initialData từ hóa đơn hiện tại
5. Submit => addPhieuThu => toast => nếu còn hóa đơn thì chuyển form tiếp
6. Nếu là form cuối => reset state và router.back()


### 10) Kiểm thử nhanh
- Truy cập: /tien-mat/hach-toan/phieu-thu?hdbh=uuid1,uuid2
  - Kỳ vọng: không hiện bảng, hiện form tạo phiếu theo thứ tự hóa đơn
  - Submit phiếu 1 => toast => chuyển phiếu 2
  - Submit phiếu 2 => toast => tự động quay lại trang trước

- Truy cập không có params
  - Kỳ vọng: hành vi cũ, danh sách hiển thị và có thể thao tác như bình thường


### 11) Mở rộng
- Có thể áp dụng pattern này cho các màn khác:
  - Tạo hook useMultipleForms tương tự, dùng useInvoiceParamsPresence (hoặc biến thể)
  - Mở rộng transform initialData theo đặc thù từng chứng từ
  - Sử dụng initialFetch trên hook data tương ứng để tránh tải không cần thiết


### 12) Troubleshooting
- Không thấy toast: đảm bảo đã import Toaster (đã có trong layout) và gọi đúng hook toast
- Không back về trang trước: kiểm tra history; có thể thêm fallback router.push đến một route mặc định
- Không hiện form khi có params: kiểm tra hasInvoiceParams, kiểm tra giá trị params, UUID hợp lệ, và entity.slug có sẵn
- initialData thiếu tk_data: kiểm tra danh mục Tài khoản có prefix 1111 và đã load xong


### 13) Code layout chính (tham khảo)
- hooks/useMultipleForms.ts: chứa logic MultipleForms
- hooks/useDataTables.tsx: nhận options.initialFetch để skip fetch khi cần
- index.tsx: nối ghép UI + hook để render form hoặc trang chính

Hoàn tất.

