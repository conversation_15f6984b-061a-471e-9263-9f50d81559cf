import { useState, useEffect, useCallback } from 'react';
import type { ApiResponse } from '@/types/api.type';
import { useAuth } from '@/contexts/auth-context';
import api from '@/lib/api';

// Generic interfaces for reusable data hook
interface UseDataConfig {
  endpoint: string;
  searchData?: any;
  pageSize?: number;
}

interface UseDataReturn<TData, TInput> {
  data: TData[];
  isLoading: boolean;
  error: string | null;
  totalItems: number;
  currentPage: number;
  handlePageChange: (page: number) => Promise<void>;
  getReport: (data: TInput) => Promise<void>;
  refreshData: () => Promise<void>;
}

/**
 * Generic reusable data hook for CRUD operations
 * @param config - Configuration object with endpoint and optional queryKey
 * @param initialList - Initial data list (optional)
 * @param searchData - Search parameters (optional)
 * @returns Hook return object with data and CRUD operations
 */
export const useReport = <TData extends { uuid: string }, TInput = Partial<TData>>(
  config: UseDataConfig,
  initialList?: TData[]
): UseDataReturn<TData, TInput> => {
  const [data, setData] = useState<TData[]>(initialList || []);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(0);
  const [lastSearchData, setLastSearchData] = useState<TInput | null>(null);

  const { entity } = useAuth();
  const { endpoint, pageSize = 10 } = config;

  const fetchData = useCallback(
    async (paginationParams?: { page?: number; pageSize?: number }, searchData?: TInput) => {
      if (!entity?.slug) return;

      setIsLoading(true);
      try {
        const params = {
          ...config.searchData,
          page: paginationParams?.page || 0,
          page_size: paginationParams?.pageSize || pageSize
        };

        const response = await api.post<ApiResponse<TData>>(
          `/entities/${entity.slug}/erp/${endpoint}/`,
          searchData || lastSearchData,
          { params }
        );

        setData(response.data.results || []);
        setTotalItems(response.data.count || 0);
        setError(null);
      } catch (error: any) {
        setError(error.message || 'Có lỗi xảy ra khi tải dữ liệu');
        setData([]);
        setTotalItems(0);
      } finally {
        setIsLoading(false);
      }
    },
    [entity?.slug, endpoint, config.searchData, pageSize, lastSearchData]
  );

  const getReport = async (itemData: TInput): Promise<void> => {
    setLastSearchData(itemData);
    setCurrentPage(0);
    await fetchData({ page: 0, pageSize }, itemData);
  };

  // Handle page change for pagination
  const handlePageChange = useCallback(
    async (page: number) => {
      setCurrentPage(page);
      await fetchData({ page, pageSize });
    },
    [fetchData, pageSize]
  );

  // Refresh function that maintains current pagination
  const refreshData = useCallback(async (): Promise<void> => {
    await fetchData({ page: currentPage, pageSize });
  }, [fetchData, currentPage, pageSize]);

  return {
    data,
    isLoading,
    error,
    totalItems,
    currentPage,
    handlePageChange,
    getReport,
    refreshData
  };
};
