'use client';

import {useState } from 'react';

import { FormDialog, ActionBar } from './components';
import { LoadingOverlay, AritoDataTables } from '@/components/custom/arito';
import { getDataTableColumnsCreate } from './cols-definition';
import { useCRUD, useFormState, useRows } from '@/hooks';
import { QUERY_KEYS } from '@/constants';
import { useRouter } from 'next/navigation';

export default function TaoPhieuThuTuHoaDonPage() {
  const router = useRouter();
  const [showSearchDialog, setShowSearchDialog] = useState(false);
  const [selectedInvoices, setSelectedInvoices] = useState<string[]>([]);
  const { isLoading, data, totalItems, currentPage, handlePageChange } =
    useCRUD<any, any>({
      endpoint: QUERY_KEYS.HOA_DON_BAN_DICH_VU
    });
  const { selectedRowIndex, handleRowClick } = useRows();
  const {
    showForm,
  } = useFormState();
  const handleSearch = () => {
    setShowSearchDialog(true);
  };

  const handleSearchClose = () => {
    setShowSearchDialog(false);
  };

  const handleSearchSubmit = (filters: any) => {};

  // Hàm xử lý khi checkbox được tick/untick
  const handleCheckboxChange = (invoiceUuid: string, field: string, newValue: boolean) => {
    let updatedSelectedInvoices: string[];

    if (newValue) {
      updatedSelectedInvoices = [...selectedInvoices, invoiceUuid];
      setSelectedInvoices(updatedSelectedInvoices);
    } else {
      updatedSelectedInvoices = selectedInvoices.filter(id => id !== invoiceUuid);
      setSelectedInvoices(updatedSelectedInvoices);
    }
  };

  const onCreateClick = () => {
    router.push(`/${QUERY_KEYS.PHIEU_THU}?hdbdv=${selectedInvoices.join(',')}`);
  };

  const tables = [
    {
      name: 'Tất cả',
      rows: data,
      columns: getDataTableColumnsCreate(handleCheckboxChange, selectedInvoices)
    },
  ];

  return (
    <div className='flex h-full min-h-[calc(100vh-120px)] w-full flex-col overflow-hidden'>
      {showSearchDialog && (
        <FormDialog open={showSearchDialog} onClose={handleSearchClose} onSubmit={handleSearchSubmit} />
      )}

      {!showForm && (
        <>
          <ActionBar
            onSearchClick={handleSearch}
            onRefreshClick={handleSearch}
            onCreateReceiptClick={onCreateClick}
            onDeleteReceiptClick={handleSearch}
          />

          {isLoading && (
            <div className='flex h-full items-center justify-center'>
              <LoadingOverlay />
            </div>
          )}

          {!isLoading && (
            <div className='w-full overflow-hidden'>
              <AritoDataTables
                tables={tables}
                onRowClick={handleRowClick}
                totalItems={totalItems}
                currentPage={currentPage}
                onPageChange={handlePageChange}
                serverSidePagination={true}
                selectedRowId={selectedRowIndex || undefined}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
}
