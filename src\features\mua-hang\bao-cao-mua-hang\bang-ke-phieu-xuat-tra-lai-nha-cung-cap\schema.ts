import { z } from 'zod';
import { dateLike } from '@/schemas/field-schemas';

export const searchSchema = z.object({
  ngay_ct1: dateLike,
  ngay_ct2: dateLike,
  so_ct1: z.string().optional(),
  so_ct2: z.string().optional(),

  dien_giai: z.string().optional(),
  mau_bc: z.number().optional(),

  report_filtering: z.string().optional(),
  data_analysis_struct: z.string().optional()
});

export const initialValues = {
  ngay_ct1: new Date().toISOString().split('T')[0],
  ngay_ct2: new Date().toISOString().split('T')[0],
  so_ct1: '',
  so_ct2: '',

  dien_giai: '',
  mau_bc: 20,

  report_filtering: '0',
  data_analysis_struct: '0'
};

export type SearchFormValues = z.infer<typeof searchSchema>;
