import StaticGenerationSearchParamsBailoutProvider from 'next/dist/client/components/static-generation-searchparams-bailout-provider';
import { format } from 'date-fns';
import { transformDocumentNumber } from '@/components/custom/arito/form/document-number/util';
import { isValidUUID } from '@/lib/uuid-validator';
import { calculateTotals } from './calc-util';
import { MA_CHUNG_TU } from '@/constants';
import { FormFieldState } from '../hooks';
/**
 * Round number to 2 decimal places
 */
const roundToTwoDecimals = (num: number): number => {
  return Math.round((num + Number.EPSILON) * 100) / 100;
};

/**
 * Transform detail rows for API submission
 * @param detailRows - Array of detail row data from the form
 * @returns Transformed detail rows ready for API submission
 */
export const transformDetailRows = (detailRows: any[]) => {
  return detailRows.map((row: any, index: number) => ({
    line: index + 1,
    ma_vt: row.ma_vt_data?.uuid || row.ma_vt || '',
    dvt: row.ma_vt_data?.dvt || row.dvt || '',
    // ten_dvt: row.ma_vt_data?.ten_dvt || row.ten_dvt || '',

    ma_kho: row.ma_vt_data?.ma_kho_data?.uuid || row.ma_kho || '',
    ten_kho: row.ma_vt_data?.ma_kho_data?.ten_kho || row.ten_kho || '',

    ma_lo: row.ma_lo_data?.uuid || row.ma_lo || '',
    ten_lo: row.ma_lo_data?.ten_lo || row.ten_lo || '',
    lo_yn: row.lo_yn || false,

    ma_vi_tri: row.ma_vi_tri_data?.uuid || row.ma_vi_tri || '',
    ten_vi_tri: row.ma_vi_tri_data?.ten_vi_tri || row.ten_vi_tri || '',
    vi_tri_yn: row.vi_tri_yn || false,

    he_so: row.he_so || 1,
    qc_yn: row.qc_yn || false,
    so_luong: row.so_luong || 0,
    pn_tb: row.pn_tb || false,

    gia_nt: row.gia_nt || 0,
    tien_nt: row.tien_nt || 0,
    gia: row.gia || 0,
    tien: row.tien || 0,

    tk_vt: row.tk_vt_data?.uuid || '',
    ma_nx: row.ma_nx_data?.uuid || row.ma_nx || '',
    tk_du: row.tk_du_data?.uuid || row.tk_du || '',

    ma_bp: row.ma_bp_data?.uuid || row.ma_bp || '',
    ma_vv: row.ma_vv_data?.uuid || row.ma_vv || '',
    ma_hd: row.ma_hd_data?.uuid || row.ma_hd || '',
    ma_dtt: row.ma_dtt_data?.uuid || row.ma_dtt || '',
    ma_ku: row.ma_ku_data?.uuid || row.ma_ku || '',
    ma_phi: row.ma_phi_data?.uuid || row.ma_phi || '',
    ma_sp: row.ma_sp_data?.uuid || row.ma_sp || '',
    ma_lsx: row.ma_lsx_data?.uuid || row.ma_lsx || '',
    ma_cp0: row.ma_cp0_data?.uuid || row.ma_cp0 || '',

    sl_pn: row.sl_pn || 0,
    id_pn: row.id_pn || 0,
    line_pn: row.line_pn || 0,
    id_sx1: row.id_sx1 || 0,
    line_sx1: row.line_sx1 || 0
  }));
};

/**
 * Transform all form data for submission
 * @param data - Form data from the form submission
 * @param state - Form field state containing references to selected entities
 * @param detailRows - Array of detail row data
 * @param entityUnit - Entity unit information
 * @param isEdit - Boolean indicating whether the form is in edit mode
 * @returns Transformed data ready for API submission
 */
export const transformFormData = (
  data: any,
  state: FormFieldState,
  detailRows: any[] = [],
  entityUnit: any,
  isEdit: boolean = false
) => {
  const chi_tiet = transformDetailRows(detailRows);
  const totals = calculateTotals(detailRows);
  return {
    unit_id: entityUnit?.uuid || '',
    ma_ngv: data.ma_ngv || '5',
    ma_gd: data.ma_gd || '',
    ma_kh: state.khachHang?.uuid || data.ma_kh || '',
    dien_giai: data.dien_giai || '',

    ...transformDocumentNumber(state.quyenChungTu, state.soChungTu, MA_CHUNG_TU.TON_KHO.PHIEU_NHAP_KHO),
    ngay_ct: data.ngay_ct || '',
    ngay_lct: isEdit ? data.ngay_lct : format(new Date(), 'yyyy-MM-dd'),
    ma_nt: state.ngoaiTe?.uuid || data.ma_nt || 'VND',
    ty_gia: roundToTwoDecimals(parseFloat(data.ty_gia) || 1),
    status: data.status || '0',
    transfer_yn: data.transfer_yn || false,
    id_progress: 0,
    action: 'create',

    t_so_luong: totals.t_so_luong,
    t_tien_nt: totals.t_tien_nt,
    t_tien: totals.t_tien,

    chi_tiet
  };
};
