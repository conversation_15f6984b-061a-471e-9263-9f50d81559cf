import React from 'react';
import { getReceiptVoucherColumns } from '../cols-definition';
import { PhieuThu, PhieuThuInput } from '@/types/schemas';
import { useCRUD } from '@/hooks/queries';
import { QUERY_KEYS } from '@/constants';

interface UseDataTablesReturn {
  phieuThus: PhieuThu[];
  addPhieuThu: (data: PhieuThuInput) => Promise<PhieuThu>;
  updatePhieuThu: (uuid: string, data: PhieuThuInput) => Promise<PhieuThu>;
  deletePhieuThu: (uuid: string) => Promise<void>;
  refreshPhieuThus: () => Promise<void>;
  allRows: PhieuThu[];
  pendingApprovalRows: PhieuThu[];
  notPostedRows: PhieuThu[];
  postedRows: PhieuThu[];
  otherRows: PhieuThu[];
  tables: any[];
  isLoading: boolean;
}

interface UseDataTablesOptions {
  initialFetch?: boolean;
}

export const useDataTables = (
  handleOpenViewDialog: () => void,
  options?: UseDataTablesOptions
): UseDataTablesReturn => {
  const {
    data: phieuThus,
    addItem: addPhieuThu,
    updateItem: updatePhieuThu,
    deleteItem: deletePhieuThu,
    refreshData: refreshPhieuThus,
    isLoading
  } = useCRUD<PhieuThu, PhieuThuInput>({
    endpoint: QUERY_KEYS.PHIEU_THU,
    initialFetch: options?.initialFetch
  });

  const allRows = phieuThus || [];
  const pendingApprovalRows = (phieuThus || []).filter((row: PhieuThu) => row.status === '3');
  const notPostedRows = (phieuThus || []).filter((row: PhieuThu) => row.status === '0');
  const postedRows = (phieuThus || []).filter((row: PhieuThu) => row.status === '5');
  const otherRows = (phieuThus || []).filter(
    (row: PhieuThu) => row.status !== '0' && row.status !== '3' && row.status !== '5'
  );

  const tables = [
    {
      name: 'Tất cả',
      rows: allRows,
      columns: getReceiptVoucherColumns(handleOpenViewDialog)
    },
    {
      name: 'Chưa ghi sổ',
      rows: notPostedRows,
      columns: getReceiptVoucherColumns(handleOpenViewDialog),
      icon: <div className='mr-2 size-[7px] rounded-full' style={{ backgroundColor: '#EAD1DC' }} />
    },
    {
      name: 'Chờ duyệt',
      rows: pendingApprovalRows,
      columns: getReceiptVoucherColumns(handleOpenViewDialog),
      icon: <div className='mr-2 size-[7px] rounded-full' style={{ backgroundColor: '#FF0000' }} />
    },
    {
      name: 'Đã ghi sổ',
      rows: postedRows,
      columns: getReceiptVoucherColumns(handleOpenViewDialog),
      icon: <div className='mr-2 size-[7px] rounded-full' style={{ backgroundColor: '#3D85C6' }} />
    },
    {
      name: 'Khác',
      rows: otherRows,
      columns: getReceiptVoucherColumns(handleOpenViewDialog),
      icon: <div className='mr-2 size-[7px] rounded-full' style={{ backgroundColor: 'black' }} />
    }
  ];

  return {
    phieuThus,
    addPhieuThu,
    updatePhieuThu,
    deletePhieuThu,
    refreshPhieuThus,
    isLoading,

    allRows,
    pendingApprovalRows,
    notPostedRows,
    postedRows,
    otherRows,

    tables
  };
};
