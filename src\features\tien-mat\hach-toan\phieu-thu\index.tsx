'use client';

import { useEffect, useState, useCallback, useMemo } from 'react';
import { useSearchParams } from 'next/navigation';
import Split from 'react-split';
import { AritoDataTables, LoadingOverlay, InputTable, DeleteDialog } from '@/components/custom/arito';
import { SearchDialog, ActionBar, PhieuThuForm, InputTableActionBar } from './components';
import { useDataTables, useSearchFieldStates, usePhieuThuDetail } from './hooks';
import { PhieuThuInput, PhieuThu, type TaiKhoan } from '@/types/schemas';
import { transformDataFromHoaDon } from './utils/transform-data';
import { getInputTableColumns } from './cols-definition';
import { useRows, useFormState, useCRUD } from '@/hooks';
import { useAuth } from '@/contexts/auth-context';
import { QUERY_KEYS } from '@/constants';
import api from '@/lib/api';

export default function PhieuThuPage() {
  const searchParams = useSearchParams();
  const { entity } = useAuth();
  const [invoiceData, setInvoiceData] = useState<any[]>([]);
  const [currentFormIndex, setCurrentFormIndex] = useState(0);
  const taiKhoanSearchData = useMemo(() => ({ prefix: '1111' }), []);
  const { data: taiKhoan, isLoading: isLoadingTaiKhoan } = useCRUD<TaiKhoan, any>({
    endpoint: QUERY_KEYS.TAI_KHOAN,
    searchData: taiKhoanSearchData
  });
  const { selectedObj, handleRowClick, clearSelection, selectedRowIndex } = useRows<PhieuThu>();
  const { handleViewClick } = useFormState();

  // Determine if URL has invoice params; if so, skip initial list fetch and auto open form flow
  const hasInvoiceParams = Boolean(searchParams.get('hdbh') || searchParams.get('hdbdv'));
  const [showMultipleForms, setShowMultipleForms] = useState(false);

  const { addPhieuThu, updatePhieuThu, deletePhieuThu, refreshPhieuThus, tables, isLoading } = useDataTables(
    handleViewClick,
    { initialFetch: !hasInvoiceParams }
  );
  const {
    showForm,
    showSearch,
    showDelete,
    formMode,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleDeleteClick,
    handleCopyClick,

    handleSearch,
    handleCloseSearch
  } = useFormState();
  const { detail, fetchDetail } = usePhieuThuDetail(selectedObj?.uuid);

  // Function to fetch invoice data by UUID
  const fetchInvoiceByUuid = useCallback(
    async (uuid: string, isHoaDonBanHang: boolean = false) => {
      if (!entity?.slug) return null;

      try {
        const endpoint = isHoaDonBanHang ? QUERY_KEYS.HOA_DON_BAN_HANG : QUERY_KEYS.HOA_DON_BAN_DICH_VU;
        const response = await api.get(`/entities/${entity.slug}/erp/${endpoint}/${uuid}/`);
        return response.data;
      } catch (error) {
        return null;
      }
    },
    [entity?.slug]
  );

  // Handle URL params for creating phieu thu from invoices
  useEffect(() => {
    const hdbhParam = searchParams.get('hdbh');
    const hdbdvParam = searchParams.get('hdbdv');

    if (hdbhParam || hdbdvParam) {
      // Immediately hide main page by enabling multi-form flow
      setShowMultipleForms(true);

      const invoiceUuids = (hdbhParam || hdbdvParam)?.split(',').filter(Boolean) || [];
      const isHoaDonBanHang = !!hdbhParam; // true if hdbh param exists

      if (invoiceUuids.length > 0) {
        // Fetch invoice data for all UUIDs
        Promise.all(invoiceUuids.map(uuid => fetchInvoiceByUuid(uuid, isHoaDonBanHang)))
          .then(invoices => {
            const validInvoices = invoices.filter(Boolean);
            setInvoiceData(validInvoices);
            if (validInvoices.length > 0) {
              setCurrentFormIndex(0);
            }
          })
          .catch(error => {
            console.error('Error fetching invoices:', error);
          });
      }
    }
  }, [searchParams, entity?.slug, fetchInvoiceByUuid]);

  // Handle form submission for multiple forms
  const handleMultipleFormSubmit = async (data: PhieuThuInput) => {
    try {
      await addPhieuThu(data);
      // Move to next form or close if this was the last one
      if (currentFormIndex < invoiceData.length - 1) {
        setShowMultipleForms(true);
        setCurrentFormIndex(currentFormIndex + 1);
      } else {
        // All forms completed
        setShowMultipleForms(false);
        setInvoiceData([]);
        setCurrentFormIndex(0);
      }
    } catch (error) {
      return;
    }
  };

  // Handle closing multiple forms
  const handleCloseMultipleForms = () => {
    setShowMultipleForms(false);
    setInvoiceData([]);
    setCurrentFormIndex(0);
  };

  const handleFormSubmit = async (data: PhieuThuInput) => {
    try {
      if (formMode === 'add') {
        await addPhieuThu(data);
      } else if (formMode === 'edit' && selectedObj) {
        await updatePhieuThu(selectedObj.uuid, data);
      }

      handleCloseForm();
      clearSelection();
      await refreshPhieuThus();
    } catch (error) {
      return;
    }
  };

  const handleSearchSubmit = (filters: any) => {};
  let initialData = transformDataFromHoaDon(invoiceData[currentFormIndex]);

  // Truyền taiKhoan vào initialData khi đã load xong
  if (!isLoadingTaiKhoan && taiKhoan && taiKhoan.length > 0) {
    initialData = {
      ...initialData,
      tk_data: taiKhoan[0] as any
    };
  }
  return (
    <>
      <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
        {/* Multiple forms for creating phieu thu from invoices */}
        {showMultipleForms && invoiceData.length > 0 && (
          <>
            <PhieuThuForm
              formMode='add'
              initialData={initialData}
              onSubmit={handleMultipleFormSubmit}
              onClose={handleCloseMultipleForms}
            />
          </>
        )}

        {/* Regular form */}
        {showForm && !showMultipleForms && (
          <PhieuThuForm
            formMode={formMode}
            initialData={formMode === 'add' && !isCopyMode ? undefined : selectedObj}
            onSubmit={handleFormSubmit}
            onClose={handleCloseForm}
          />
        )}

        {showDelete && (
          <DeleteDialog
            open={showDelete}
            onClose={handleCloseDelete}
            selectedObj={selectedObj}
            deleteObj={deletePhieuThu}
            clearSelection={clearSelection}
          />
        )}

        {!showForm && !showMultipleForms && (
          <>
            <SearchDialog open={showSearch} onClose={handleCloseSearch} onSearch={handleSearchSubmit} />

            <ActionBar
              onAddClick={handleAddClick}
              onEditClick={() => selectedObj && handleEditClick()}
              onDeleteClick={() => selectedObj && handleDeleteClick()}
              onCopyClick={() => selectedObj && handleCopyClick()}
              onSearchClick={handleSearch}
              onRefreshClick={async () => {
                await refreshPhieuThus();
                await fetchDetail();
              }}
              isEditDisabled={!selectedObj}
              isDeleteDisabled={!selectedObj}
              isCopyDisabled={!selectedObj}
            />
            <Split
              className='flex flex-1 flex-col'
              direction='vertical'
              sizes={[50, 50]}
              minSize={200}
              gutterSize={8}
              gutterAlign='center'
              snapOffset={30}
              dragInterval={1}
              cursor='row-resize'
            >
              <div className='w-full overflow-hidden'>
                {isLoading && <LoadingOverlay />}
                {!isLoading && (
                  <AritoDataTables
                    tables={tables}
                    onRowClick={handleRowClick}
                    selectedRowId={selectedRowIndex || undefined}
                  />
                )}
              </div>

              <div className='w-full overflow-hidden'>
                <InputTable
                  rows={detail || []}
                  columns={getInputTableColumns()}
                  mode={formMode}
                  getRowId={row => row?.uuid || ''}
                  className='w-full'
                  actionButtons={<InputTableActionBar mode={formMode} />}
                />
              </div>
            </Split>
          </>
        )}
      </div>
    </>
  );
}
