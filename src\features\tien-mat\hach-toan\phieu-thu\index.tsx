'use client';

import Split from 'react-split';
import { useMemo } from 'react';
import { AritoDataTables, LoadingOverlay, InputTable, DeleteDialog } from '@/components/custom/arito';
import { SearchDialog, ActionBar, PhieuThuForm, InputTableActionBar } from './components';
import { usePhieuThuDetail, useMultipleForms, useInvoiceParamsPresence } from './hooks';
import { getInputTableColumns, getReceiptVoucherColumns } from './cols-definition';
import { PhieuThuInput, PhieuThu } from '@/types/schemas';
import { useRows, useFormState, useCRUD } from '@/hooks';
import { QUERY_KEYS } from '@/constants';

export default function PhieuThuPage() {
  const hasInvoiceParams = useInvoiceParamsPresence();

  const { selectedObj, handleRowClick, clearSelection, selectedRowIndex } = useRows<PhieuThu>();
  const { handleViewClick } = useFormState();

  // Use CRUD with server-side pagination for AritoDataTables
  const {
    data: phieuThus,
    addItem: addPhieuThu,
    updateItem: updatePhieuThu,
    deleteItem: deletePhieuThu,
    refreshData: refreshPhieuThus,
    totalItems,
    currentPage,
    handlePageChange,
    isLoading
  } = useCRUD<PhieuThu, PhieuThuInput>({ endpoint: QUERY_KEYS.PHIEU_THU, initialFetch: !hasInvoiceParams });

  const tables = useMemo(
    () => [
      {
        name: 'Tất cả',
        rows: phieuThus || [],
        columns: getReceiptVoucherColumns(handleViewClick)
      }
    ],
    [phieuThus, handleViewClick]
  );

  const {
    showForm,
    showSearch,
    showDelete,
    formMode,
    isCopyMode,

    handleCloseForm,
    handleCloseDelete,
    handleAddClick,
    handleEditClick,
    handleDeleteClick,
    handleCopyClick,

    handleSearch,
    handleCloseSearch
  } = useFormState();

  const { detail, fetchDetail } = usePhieuThuDetail(selectedObj?.uuid);

  const { showMultipleForms, initialData, handleMultipleFormSubmit, handleCloseMultipleForms, invoiceData } =
    useMultipleForms({ addPhieuThu });

  const handleFormSubmit = async (data: PhieuThuInput) => {
    try {
      if (formMode === 'add') {
        await addPhieuThu(data);
      } else if (formMode === 'edit' && selectedObj) {
        await updatePhieuThu(selectedObj.uuid, data);
      }

      handleCloseForm();
      clearSelection();
      await refreshPhieuThus();
    } catch (error) {
      return;
    }
  };

  const handleSearchSubmit = (filters: any) => {};

  return (
    <>
      <div className='flex h-full min-h-[calc(100vh-64px)] w-screen flex-col overflow-auto'>
        {/* Multiple forms for creating phieu thu from invoices */}
        {showMultipleForms && invoiceData.length > 0 && (
          <>
            <PhieuThuForm
              formMode='add'
              initialData={initialData}
              onSubmit={handleMultipleFormSubmit}
              onClose={handleCloseMultipleForms}
            />
          </>
        )}

        {/* Regular form */}
        {showForm && !showMultipleForms && (
          <PhieuThuForm
            formMode={formMode}
            initialData={formMode === 'add' && !isCopyMode ? undefined : selectedObj}
            onSubmit={handleFormSubmit}
            onClose={handleCloseForm}
          />
        )}

        {showDelete && (
          <DeleteDialog
            open={showDelete}
            onClose={handleCloseDelete}
            selectedObj={selectedObj}
            deleteObj={deletePhieuThu}
            clearSelection={clearSelection}
          />
        )}

        {!showForm && !showMultipleForms && (
          <>
            <SearchDialog open={showSearch} onClose={handleCloseSearch} onSearch={handleSearchSubmit} />

            <ActionBar
              onAddClick={handleAddClick}
              onEditClick={() => selectedObj && handleEditClick()}
              onDeleteClick={() => selectedObj && handleDeleteClick()}
              onCopyClick={() => selectedObj && handleCopyClick()}
              onSearchClick={handleSearch}
              onRefreshClick={async () => {
                await refreshPhieuThus();
                await fetchDetail();
              }}
              isEditDisabled={!selectedObj}
              isDeleteDisabled={!selectedObj}
              isCopyDisabled={!selectedObj}
            />
            <Split
              className='flex flex-1 flex-col'
              direction='vertical'
              sizes={[50, 50]}
              minSize={200}
              gutterSize={8}
              gutterAlign='center'
              snapOffset={30}
              dragInterval={1}
              cursor='row-resize'
            >
              <div className='w-full overflow-hidden'>
                {isLoading && <LoadingOverlay />}
                {!isLoading && (
                  <AritoDataTables
                    tables={tables}
                    onRowClick={handleRowClick}
                    selectedRowId={selectedRowIndex || undefined}
                    totalItems={totalItems}
                    currentPage={currentPage}
                    onPageChange={handlePageChange}
                    serverSidePagination
                  />
                )}
              </div>

              <div className='w-full overflow-hidden'>
                <InputTable
                  rows={detail || []}
                  columns={getInputTableColumns()}
                  mode={formMode}
                  getRowId={row => row?.uuid || ''}
                  className='w-full'
                  actionButtons={<InputTableActionBar mode={formMode} />}
                />
              </div>
            </Split>
          </>
        )}
      </div>
    </>
  );
}
