import { QuyenChungTu } from '@/types/schemas';
import type { FormMode } from '@/types/form';

/**
 * Props for the DocumentNumberField component (Simplified but Complete)
 */
export interface DocumentNumberFieldProps {
  /**
   * Document type code (MA_CHUNG_TU)
   */
  ma_ct: string;

  /**
   * Name of the date field to watch for changes (default: 'ngay_ct')
   */
  dateFieldName?: string;

  /**
   * Current selected QuyenChungTu
   */
  quyenChungTu: QuyenChungTu | null;

  /**
   * Callback when QuyenChungTu is selected
   */
  onQuyenChungTuChange: (quyenChungTu: QuyenChungTu) => void;

  /**
   * Current document number
   */
  soChungTu: string;

  /**
   * Callback when document number changes
   */
  onSoChungTuChange: (soChungTu: string) => void;

  /**
   * Whether the field is disabled
   */
  disabled?: boolean;

  /**
   * Label for the field (default: '<PERSON><PERSON> chứng từ')
   */
  label?: string;

  /**
   * CSS class for the label
   */
  labelClassName?: string;

  /**
   * CSS class for the field
   */
  className?: string;

  /**
   * CSS class for the search field
   */
  classNameSearchField?: string;

  formMode?: FormMode;
}

/**
 * Props for the useDocumentNumber hook
 */
export interface UseDocumentNumberProps {
  /**
   * Document type code (MA_CHUNG_TU)
   */
  ma_ct: string;

  /**
   * Name of the date field to watch for changes (default: 'ngay_ct')
   */
  dateFieldName?: string;

  /**
   * Initial QuyenChungTu value
   */
  initialQuyenChungTu?: QuyenChungTu | null;

  /**
   * Initial document number value
   */
  initialSoChungTu?: string;
}

/**
 * Return type for the useDocumentNumber hook
 */
export interface UseDocumentNumberReturn {
  /**
   * Current selected QuyenChungTu
   */
  quyenChungTu: QuyenChungTu | null;

  /**
   * Function to set QuyenChungTu
   */
  setQuyenChungTu: (quyenChungTu: QuyenChungTu) => void;

  /**
   * Current document number
   */
  soChungTu: string;

  /**
   * Function to set document number
   */
  setSoChungTu: (soChungTu: string) => void;

  /**
   * Available QuyenChungTu options
   */
  quyenChungTus: QuyenChungTu[];

  /**
   * Loading state
   */
  isLoading: boolean;

  /**
   * Refresh the list of QuyenChungTu based on current ma_ct and date
   */
  refreshQuyenChungTus: () => Promise<void>;
}
