import { GridColDef } from '@mui/x-data-grid';
import React from 'react';
import {
  khachHangSearchColumns,
  QUERY_KEYS,
  vuViecSearchColumns,
  hopDongSearchColumns,
  dotThanhToanSearchColumns,
  kheUocSearchColumns,
  phiSearchColumns,
  vatTuSearchColumns,
  lenhSanXuatSearchColumns,
  chiPhiKhongHopLeSearchColumns,
  hoaDonSearchColumns,
  accountSearchColumns
} from '@/constants';
import { DotThanhToan, HopDong, KhachHang, KheUoc, Phi, VuViec, VatTu, TaiKhoan } from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { SearchField } from '@/components/custom/arito';

export const getDetailItemColumns = (
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void,
  ma_ngv?: string,
  dien_giai?: string
): GridColDef[] => [
  {
    field: 'dien_giai',
    headerName: 'Diễn giải',
    width: 200,
    renderCell: (params: any) => (
      <CellField
        name='dien_giai'
        type='text'
        value={params.row.dien_giai || dien_giai || ''}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'dien_giai', newValue)}
      />
    )
  },
  {
    field: 'ma_kh',
    headerName: 'Mã đối tượng',
    width: 150,
    renderCell: (params: any) => (
      <SearchField<KhachHang>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHACH_HANG}/`}
        searchColumns={khachHangSearchColumns}
        columnDisplay='customer_code'
        dialogTitle='Danh mục đối tượng'
        value={params.row.ma_kh_data?.customer_code || ''}
        onRowSelection={(row: any) => {
          onCellValueChange(params.row.uuid, 'ma_kh_data', row);
        }}
      />
    )
  },
  {
    field: 'ten_kh',
    headerName: 'Tên đối tượng',
    width: 200,
    renderCell: (params: any) => (
      <CellField name='ten_kh' type='text' value={params.row.ma_kh_data?.customer_name || ''} />
    )
  },
  {
    field: 'du_cn',
    headerName: 'Dư công nợ',
    width: 120,
    renderCell: (params: any) => (
      <CellField name='du_cn' type='number' value={params.row.ma_kh_data?.credit_limit || 0} />
    )
  },
  ...(ma_ngv === '1'
    ? [
        {
          field: 'id_hd',
          headerName: 'Hóa đơn',
          width: 180,
          renderCell: (params: any) => (
            <SearchField<any>
              type='text'
              searchEndpoint={`/${QUERY_KEYS.HOA_DON_THEO_KHACH_HANG}/?customer=${params.row.ma_kh_data?.uuid}&context=GBC`}
              searchColumns={hoaDonSearchColumns}
              columnDisplay='so_ct'
              dialogTitle='Hóa đơn'
              disabled={!params.row.ma_kh_data?.uuid}
              value={params.row.id_hd_data?.so_ct || ''}
              onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'id_hd_data', row)}
            />
          )
        },
        {
          field: 'so_ct0_hd',
          headerName: 'Số hóa đơn',
          width: 120,
          renderCell: (params: any) => (
            <CellField name='so_ct0_hd' type='text' value={params.row.id_hd_data?.so_hd || ''} />
          )
        },
        {
          field: 'ngay_ct_hd',
          headerName: 'Ngày hóa đơn',
          width: 140,
          renderCell: (params: any) => (
            <CellField name='ngay_ct_hd' type='date' value={params.row.id_hd_data?.ngay_ct || ''} />
          )
        }
      ]
    : []),
  {
    field: 'tk_co',
    headerName: 'Tài khoản có',
    width: 100,
    renderCell: (params: any) => {
      if (ma_ngv === '1') {
        return <CellField name='tk_co' type='text' value={params.row.id_hd_data?.tk_data?.tk || ''} />;
      }

      return (
        <SearchField<TaiKhoan>
          searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}/`}
          searchColumns={accountSearchColumns}
          columnDisplay='code'
          dialogTitle='Danh mục tài khoản'
          value={params.row.tk_co_data?.code}
          onRowSelection={(row: any) => {
            onCellValueChange(params.row.uuid, 'tk_co_data', row);
          }}
        />
      );
    }
  },
  ...(ma_ngv === '1'
    ? [
        {
          field: 'ma_nt_hd',
          headerName: 'Ngoại tệ',
          width: 100,
          renderCell: (params: any) => (
            <CellField
              name='ma_nt_hd'
              type='text'
              value={params.row.id_hd_data?.ngoai_te || ''}
              onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'ma_nt_hd', newValue)}
            />
          )
        },
        {
          field: 'ty_gia_hd',
          headerName: 'Tỷ giá hđ',
          width: 100,
          renderCell: (params: any) => (
            <CellField
              name='ty_gia_hd'
              type='number'
              value={params.row.id_hd_data?.ty_gia || 1}
              onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'ty_gia_hd', newValue)}
            />
          )
        },
        {
          field: 'tien_hd_nt',
          headerName: 'Tiền trên hoá đơn',
          width: 140,
          renderCell: (params: any) => (
            <CellField
              name='tien_hd_nt'
              type='number'
              value={params.row.id_hd_data?.tien_tren_hd || 0}
              onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'tien_hd_nt', newValue)}
            />
          )
        },
        {
          field: 'da_pb_nt',
          headerName: 'Đã phân bổ',
          width: 100
        },
        {
          field: 'cl_nt',
          headerName: 'Còn lại',
          width: 140,
          renderCell: (params: any) => (
            <CellField
              name='cl_nt'
              type='number'
              value={params.row.id_hd_data?.tien_con_phai_tt || 0}
              onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'cl_nt', newValue)}
            />
          )
        }
      ]
    : []),
  ...(ma_ngv !== '1'
    ? [
        {
          field: 'ten_tk_co',
          headerName: 'Tên tài khoản',
          width: 150,
          renderCell: (params: any) => (
            <CellField name='ten_tk_co' type='text' value={params.row.tk_co_data?.name || ''} />
          )
        }
      ]
    : []),
  {
    field: 'tien_nt',
    headerName: 'Tiền VND',
    width: 130,
    renderCell: (params: any) => (
      <CellField
        name='tien_nt'
        type='number'
        value={params.row.tien_nt || params.row.id_hd_data?.tien_hd_nt || 0}
        onValueChange={(newValue: any) => onCellValueChange(params.row.uuid, 'tien_nt', newValue)}
      />
    )
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120,
    renderCell: (params: any) => (
      <SearchField<VuViec>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VU_VIEC}/`}
        searchColumns={vuViecSearchColumns}
        columnDisplay='ma_vu_viec'
        dialogTitle='Danh mục vụ việc'
        value={params.row.ma_vv_data?.ma_vv || params.row.ma_vv_data?.ma_vu_viec || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_vv_data', row)}
      />
    )
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120,
    renderCell: (params: any) => (
      <SearchField<HopDong>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.HOP_DONG}/`}
        searchColumns={hopDongSearchColumns}
        columnDisplay='ma_hd'
        dialogTitle='Danh mục hợp đồng'
        value={params.row.ma_hd_data?.ma_hd || params.row.ma_hd || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_hd_data', row)}
      />
    )
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 120,
    renderCell: (params: any) => (
      <SearchField<DotThanhToan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}/`}
        searchColumns={dotThanhToanSearchColumns}
        columnDisplay='ma_dtt'
        dialogTitle='Danh mục đợt thanh toán'
        value={params.row.ma_dtt_data?.ma_dtt || params.row.ma_dtt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_dtt_data', row)}
      />
    )
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120,
    renderCell: (params: any) => (
      <SearchField<KheUoc>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHE_UOC}/`}
        searchColumns={kheUocSearchColumns}
        columnDisplay='ma_ku'
        dialogTitle='Danh mục khế ước'
        value={params.row.ma_ku_data?.ma_ku || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_ku_data', row)}
      />
    )
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 120,
    renderCell: (params: any) => (
      <SearchField<Phi>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.PHI}/`}
        searchColumns={phiSearchColumns}
        columnDisplay='ma_phi'
        dialogTitle='Danh mục phí'
        value={params.row.ma_phi_data?.ma_phi || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_phi_data', row)}
      />
    )
  },
  {
    field: 'ma_sp',
    headerName: 'Sản phẩm',
    width: 120,
    renderCell: (params: any) => (
      <SearchField<VatTu>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VAT_TU}/`}
        searchColumns={vatTuSearchColumns}
        columnDisplay='ma_vt'
        dialogTitle='Danh mục sản phẩm'
        value={params.row.ma_sp_data?.ma_vt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_sp_data', row)}
      />
    )
  },
  {
    field: 'ma_lsx',
    headerName: 'Lệnh sản xuất',
    width: 120,
    renderCell: (params: any) => (
      <SearchField<any>
        type='text'
        searchEndpoint={'/'}
        searchColumns={lenhSanXuatSearchColumns}
        columnDisplay='ma_lsx'
        dialogTitle='Danh mục lệnh sản xuất'
        value={params.row.ma_lsx_data?.ma_lsx || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_lsx_data', row)}
      />
    )
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 120,
    renderCell: (params: any) => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.CHI_PHI_KHONG_HOP_LE}/`}
        searchColumns={chiPhiKhongHopLeSearchColumns}
        columnDisplay='ma_cpkhl'
        dialogTitle='Danh mục chi phí không hợp lệ'
        value={params.row.ma_cp0_data?.ma_cpkhl || params.row.ma_cp0 || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_cp0_data', row)}
      />
    )
  }
];
