'use client';

import React, { useCallback, useEffect } from 'react';
import { QUERY_KEYS, quyenChungTuSearchColumns } from '@/constants';
import { FormField, SearchField } from '@/components/custom/arito';
import { useDocumentNumber } from './hooks/useDocumentNumber';
import { DocumentNumberFieldProps } from './types';
import { QuyenChungTu } from '@/types/schemas';
import { Label } from '@/components/ui/label';

/**
 * DocumentNumberField Component (Simplified but Functional)
 *
 * A component for handling document number selection and generation.
 * This component combines QuyenChungTu selection with automatic document number generation.
 * Simplified to prevent infinite re-renders while keeping all functionality.
 *
 * Features:
 * - Automatic QuyenChungTu fetching based on document type and date
 * - Auto-generation of document numbers when QuyenChungTu is selected
 * - Consistent styling with other form components
 * - Optimized to prevent infinite loops
 *
 * @param props - Component configuration props
 */
export const DocumentNumberField: React.FC<DocumentNumberFieldProps> = ({
  ma_ct,
  dateFieldName = 'ngay_ct',
  quyenChungTu,
  onQuyenChungTuChange,
  soChungTu,
  onSoChungTuChange,
  disabled = false,
  label = 'Số chứng từ',
  labelClassName = 'w-32 min-w-32',
  className = 'flex items-center',
  classNameSearchField,
  formMode = 'add'
}) => {
  // Use the simplified hook for document number management
  const {
    quyenChungTus,
    soChungTu: hookSoChungTu,
    setSoChungTu: setHookSoChungTu,
    setQuyenChungTu: setHookQuyenChungTu,
    refreshQuyenChungTus
  } = useDocumentNumber({
    ma_ct,
    dateFieldName,
    initialQuyenChungTu: quyenChungTu,
    initialSoChungTu: soChungTu
  });

  // Use the hook's soChungTu if available, otherwise fall back to prop
  const displaySoChungTu = hookSoChungTu || soChungTu || '';

  // Handle QuyenChungTu selection - update both hook and parent
  const handleQuyenChungTuChange = useCallback(
    (selectedQuyenChungTu: QuyenChungTu) => {
      setHookQuyenChungTu(selectedQuyenChungTu);
      onQuyenChungTuChange(selectedQuyenChungTu);
    },
    [setHookQuyenChungTu, onQuyenChungTuChange]
  );

  // Handle soChungTu change - update both hook and parent
  const handleSoChungTuChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;
      setHookSoChungTu(newValue);
      onSoChungTuChange(newValue);
    },
    [setHookSoChungTu, onSoChungTuChange]
  );

  // Sync hook's generated soChungTu back to parent when it changes (debounced)
  useEffect(() => {
    if (hookSoChungTu && hookSoChungTu !== soChungTu) {
      // Use a timeout to debounce the update and prevent infinite loops
      const timeoutId = setTimeout(() => {
        onSoChungTuChange(hookSoChungTu);
      }, 100);

      return () => clearTimeout(timeoutId);
    }
  }, [hookSoChungTu, soChungTu, onSoChungTuChange]);

  return (
    <div className={className}>
      <Label className={labelClassName}>{label}</Label>
      <div className={classNameSearchField}>
        {formMode === 'add' && (
          <SearchField<QuyenChungTu>
            type='text'
            value={displaySoChungTu}
            onValueChange={handleSoChungTuChange}
            searchEndpoint={`/${QUERY_KEYS.QUYEN_CHUNG_TU}/`}
            searchColumns={quyenChungTuSearchColumns}
            disabled={disabled}
            dialogTitle='Danh mục quyển'
            columnDisplay='ma_nk'
            rows={quyenChungTus.length > 0 ? quyenChungTus : undefined}
            onRowSelection={handleQuyenChungTuChange}
            onBeforeOpen={async () => {
              await refreshQuyenChungTus?.();
            }}
          />
        )}
        {formMode !== 'add' && <FormField name='so_ct' type='text' className='w-full' disabled />}
      </div>
    </div>
  );
};

/**
 * DocumentNumberHook
 *
 * A hook-only version for cases where you need the logic but want to render your own UI.
 * This provides the same functionality as DocumentNumberField but without the UI component.
 *
 * @param props - Hook configuration props
 * @returns Document number state and handlers
 */
export { useDocumentNumber as DocumentNumberHook } from './hooks/useDocumentNumber';

// Export types for external use
export type { DocumentNumberFieldProps, UseDocumentNumberProps, UseDocumentNumberReturn } from './types';
