/**
 * Map thông tin từ hóa đơn bán hàng sang giấy báo có
 */
export const mapHoaDonToGiayBaoCo = (hoaDonInfo: any, chiTietList: any[] = []) => {
  return {
    formFields: {},

    chi_tiet:
      chiTietList.length > 0
        ? chiTietList.map((item, index) => ({
            ma_kh_data: hoaDonInfo.ma_kh_data,
            id_hd_data: {
              ID: hoaDonInfo.uuid,
              so_ct: hoaDonInfo.so_ct,
              so_hd: hoaDonInfo.so_hd,
              ngay_ct: hoaDonInfo.ngay_ct,
              // Cho trường hợp ma_ngv === '1'
              tk_data: {
                uuid: hoaDonInfo.tk_data?.uuid,
                tk: hoaDonInfo.tk_data?.code || hoaDonInfo.tk_data?.tk
              },
              ty_gia: hoaDonInfo.ty_gia || 1,
              ty_gia_hd: hoaDonInfo.ty_gia,
              ngoai_te: hoaDonInfo.ma_nt_data?.ma_nt,
              tien_tren_hd: hoaDonInfo.t_tt,
              tien_hd_nt: hoaDonInfo.t_tt,
              tien_con_phai_tt: hoaDonInfo.t_tt
            },
            // Cho trường hợp ma_ngv !== '1'
            tk_co_data: {
              code: hoaDonInfo.tk_data?.code,
              name: hoaDonInfo.tk_data?.name
            },
            so_ct0_hd: hoaDonInfo.so_hd,
            tien_nt: hoaDonInfo.t_tt || 0,
            tien: hoaDonInfo.t_tt || 0,
            ma_vt_data: item.ma_vt_data,
            ten_vt: item.ten_vt,
            dien_giai: hoaDonInfo.dien_giai,

            ma_vv_data: item.ma_vv_data,
            ma_hd_data: item.ma_hd_data,
            ma_dtt_data: item.ma_dtt_data,
            ma_ku_data: item.ma_ku_data,
            ma_phi_data: item.ma_phi_data,
            ma_cp0_data: item.ma_cp0_data,
            ma_sp_data: item.ma_sp_data,
            ma_lsx_data: item.ma_lsx_data,
            ma_bp_data: item.ma_bp_data
          }))
        : [
            {
              uuid: `hd_${hoaDonInfo.uuid}_default`,
              ma_kh_data: hoaDonInfo.ma_kh_data,
              id_hd_data: {
                ID: hoaDonInfo.uuid,
                uuid: hoaDonInfo.uuid,
                so_ct: hoaDonInfo.so_ct,
                so_hd: hoaDonInfo.so_hd,
                ngay_ct: hoaDonInfo.ngay_ct,
                // Cho trường hợp ma_ngv === '1'
                tk_data: {
                  uuid: hoaDonInfo.tk_data?.uuid,
                  tk: hoaDonInfo.tk_data?.code || hoaDonInfo.tk_data?.tk
                },
                ty_gia: hoaDonInfo.ty_gia || 1,
                ty_gia_hd: hoaDonInfo.ty_gia,
                ngoai_te: hoaDonInfo.ma_nt_data?.ma_nt,
                tien_tren_hd: hoaDonInfo.tong_thanh_toan || hoaDonInfo.t_tt_nt || 0,
                tien_hd_nt: hoaDonInfo.tong_thanh_toan || hoaDonInfo.t_tt_nt || 0
              },
              // Cho trường hợp ma_ngv !== '1'
              tk_co_data: {
                code: hoaDonInfo.tk_data?.code || hoaDonInfo.tk_data?.tk,
                name: hoaDonInfo.tk_data?.name || hoaDonInfo.tk_data?.ten_tk
              },
              so_ct0_hd: hoaDonInfo.so_hd,
              tien_nt: hoaDonInfo.tong_thanh_toan || 0,
              tien: hoaDonInfo.tong_thanh_toan || 0,
              dien_giai: `Thanh toán hóa đơn ${hoaDonInfo.so_ct}`
            }
          ],

    metadata: {
      so_ct: hoaDonInfo.so_ct,
      ten_kh: hoaDonInfo.ten_kh || hoaDonInfo.ma_kh_data?.ten_kh,
      tong_tien: hoaDonInfo.tong_thanh_toan || 0
    }
  };
};
