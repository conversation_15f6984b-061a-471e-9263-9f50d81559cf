import { Box, TextField } from '@mui/material';
import React, { useState } from 'react';
import { searchIconButtonStyle, textFieldInputStyle } from './styles';
import { AritoIcon, AritoDialog } from '@/components/custom/arito';
import { useDialogState, useSelection } from './hooks';
import { SearchIcon } from '@/components/icons';
import { ActionsButton } from './ActionsButton';
import { DataTable } from './DataTable';
import { cn } from '@/lib/utils';

interface SearchFieldProps<T = any> {
  name?: string;
  type?: string;
  disabled?: boolean;
  className?: string;
  columnDisplay?: keyof T;
  rows?: T[];
  searchEndpoint?: string;
  otherSearchEndpoint?: string;
  searchColumns?: any[];
  displayRelatedField?: keyof T;
  classNameRelatedField?: string;
  relatedFieldValue?: string;
  dialogTitle?: string;
  hideRowNumberColumn?: boolean;
  headerFields?: React.ReactNode;
  placeholder?: string;
  value?: any;
  onValueChange?: (value: any) => void;
  onRowSelection?: (row: T) => void;
  onBeforeOpen?: () => void | Promise<void>;
}

export const SearchField = <T = any,>({
  type = 'text',
  value = '',
  searchEndpoint,
  searchColumns = [],
  className,
  dialogTitle = 'Danh mục',
  placeholder,
  onRowSelection,
  onValueChange,
  displayRelatedField,
  classNameRelatedField,
  relatedFieldValue,
  disabled,
  columnDisplay = '' as keyof T,
  rows,
  otherSearchEndpoint,
  onBeforeOpen
}: SearchFieldProps<T>) => {
  const { searchDialogOpen, setSearchDialogOpen, isFullScreen, handleSearchClick, toggleFullScreen } = useDialogState();
  const { selectedObj, setSelectedObj, clearSelection } = useSelection<T>();
  const [localSelectedObj, setLocalSelectedObj] = useState<T | null>(null);

  return (
    <div className={cn('relative flex items-center', className)}>
      <TextField
        type={type}
        disabled={disabled}
        fullWidth
        className={cn('w-40', className)}
        size='small'
        variant='standard'
        value={value || selectedObj?.[columnDisplay] || ''}
        onChange={value => onValueChange?.(value)}
        placeholder={placeholder}
        sx={{
          ...textFieldInputStyle(type),
          '& .MuiInputBase-input::placeholder': {
            opacity: 1,
            color: 'rgba(0, 0, 0, 0.6)'
          }
        }}
        InputProps={{
          endAdornment: !disabled ? (
            <Box
              sx={searchIconButtonStyle}
              onClick={async e => {
                e.preventDefault();
                e.stopPropagation();
                try {
                  await onBeforeOpen?.();
                } catch (err) {
                  // Ignore pre-open errors to not block opening dialog
                }
                handleSearchClick();
              }}
            >
              <SearchIcon size={16} color='#2563EB' />
            </Box>
          ) : null
        }}
      />

      {displayRelatedField && (
        <div
          className={cn(
            'ml-0 mt-1 flex-1 text-ellipsis whitespace-nowrap text-left text-sm text-gray-700 sm:ml-3 sm:mt-0',
            classNameRelatedField
          )}
        >
          {relatedFieldValue || (selectedObj?.[displayRelatedField] as string)}
        </div>
      )}

      <AritoDialog
        open={searchDialogOpen}
        onClose={() => {}}
        disableBackdropClose={true}
        disableEscapeKeyDown={true}
        fullScreen={isFullScreen}
        onFullscreenToggle={toggleFullScreen}
        title={dialogTitle}
        titleIcon={<AritoIcon icon={584} className='mx-2' />}
        actions={
          <ActionsButton
            selectedSearchResult={localSelectedObj}
            onConfirm={() => {
              if (localSelectedObj) {
                setSelectedObj(localSelectedObj);
                onRowSelection?.(localSelectedObj);
                setSearchDialogOpen(false);
              }
            }}
            onCancel={() => {
              clearSelection();
              setLocalSelectedObj(null);
              setSearchDialogOpen(false);
            }}
          />
        }
        classNameContent='max-h-[400px] overflow-y-auto w-[800px]'
      >
        <DataTable
          searchEndpoint={searchEndpoint}
          otherSearchEndpoint={otherSearchEndpoint}
          searchColumns={searchColumns}
          columnDisplay={columnDisplay}
          rows={rows}
          onRowSelection={setLocalSelectedObj}
          onSelectedObjChange={obj => obj && setLocalSelectedObj(obj)}
        />
      </AritoDialog>
    </div>
  );
};
