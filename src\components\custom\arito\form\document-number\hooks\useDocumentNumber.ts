import { useState, useEffect, useRef, useCallback } from 'react';
import { useFormContext } from 'react-hook-form';
import { UseDocumentNumberProps, UseDocumentNumberReturn } from '../types';
import { generateSoChungTuHienTai } from '@/lib/stringUtil';
import { useQuyenChungTuByChungTu } from '@/hooks';
import { QuyenChungTu } from '@/types/schemas';
import { getCurrentNumber } from '../util';

/**
 * Custom hook for managing document number generation and QuyenChungTu selection (Simplified)
 *
 * This hook encapsulates the logic for:
 * - Watching date field changes
 * - Fetching QuyenChungTu data based on document type and date
 * - Automatically generating document numbers when QuyenChungTu changes
 * - Simplified to prevent infinite re-renders
 *
 * @param props - Configuration options for the hook
 * @returns Object containing state and handlers for document number management
 */
export const useDocumentNumber = ({
  ma_ct,
  dateFieldName = 'ngay_ct',
  initialQuyenChungTu = null,
  initialSoChungTu = ''
}: UseDocumentNumberProps): UseDocumentNumberReturn => {
  const { watch } = useFormContext();
  const [quyenChungTu, setQuyenChungTu] = useState<QuyenChungTu | null>(initialQuyenChungTu);
  const [soChungTu, setSoChungTu] = useState<string>(initialSoChungTu);

  // Use refs to track previous values and prevent infinite loops
  const prevInitialSoChungTu = useRef<string>(initialSoChungTu);
  const prevInitialQuyenChungTu = useRef<QuyenChungTu | null>(initialQuyenChungTu);

  // Watch for date field changes
  const ngayCt = watch(dateFieldName);

  // Fetch QuyenChungTu data based on document type and date
  const { quyenChungTus, isLoading, refreshQuyenChungTus } = useQuyenChungTuByChungTu({
    ma_ct,
    ngay_hl: ngayCt || new Date().toISOString().split('T')[0]
  });

  // Sync with external soChungTu changes (only when actually different)
  useEffect(() => {
    if (initialSoChungTu !== prevInitialSoChungTu.current) {
      prevInitialSoChungTu.current = initialSoChungTu;
      setSoChungTu(initialSoChungTu);
    }
  }, [initialSoChungTu]);

  // Sync with external quyenChungTu changes (only when actually different)
  useEffect(() => {
    if (initialQuyenChungTu !== prevInitialQuyenChungTu.current) {
      prevInitialQuyenChungTu.current = initialQuyenChungTu;
      setQuyenChungTu(initialQuyenChungTu);
    }
  }, [initialQuyenChungTu]);

  // Auto-generate document number when QuyenChungTu changes
  const generateDocumentNumber = useCallback((selectedQuyenChungTu: QuyenChungTu) => {
    try {
      const generatedSoChungTu = generateSoChungTuHienTai(
        getCurrentNumber(selectedQuyenChungTu),
        selectedQuyenChungTu.so_ct_mau,
        new Date()
      );
      if (generatedSoChungTu) {
        setSoChungTu(generatedSoChungTu);
        return generatedSoChungTu;
      }
    } catch (error) {
      console.error('Error generating document number:', error);
      // Fallback to a simple format if generation fails
      const fallbackNumber = `${selectedQuyenChungTu.ma_nk || 'DOC'}-${new Date().getTime()}`;
      setSoChungTu(fallbackNumber);
      return fallbackNumber;
    }
    return '';
  }, []);

  // Memoized setQuyenChungTu that also generates document number
  const handleSetQuyenChungTu = useCallback(
    (newQuyenChungTu: QuyenChungTu | null) => {
      setQuyenChungTu(newQuyenChungTu);
      if (newQuyenChungTu) {
        generateDocumentNumber(newQuyenChungTu);
      }
    },
    [generateDocumentNumber]
  );

  return {
    quyenChungTu,
    setQuyenChungTu: handleSetQuyenChungTu,
    soChungTu,
    setSoChungTu,
    quyenChungTus,
    isLoading,
    refreshQuyenChungTus
  };
};
