import { GridColDef, GridRenderCellParams } from '@mui/x-data-grid';
import {
  QUERY_KEYS,
  accountSearchColumns,
  boPhanSearchColumns,
  chiPhiKhongHopLeSearchColumns,
  chiPhiSearchColumns,
  dotThanhToanSearchColumns,
  hopDongSearchColumns,
  kheUocSearchColumns,
  lenhSanXuatSearchColumns,
  phiSearchColumns,
  lyDoNhapXuatSearchColumns,
  paymentInstallmentSearchColumns,
  thueSearchColumns,
  vatTu1SearchColumns,
  vatTuSearchColumns,
  vuViecSearchColumns,
  warehouseSearchColumns
} from '@/constants';
import {
  AccountModel,
  BoPhan,
  ChiPhi,
  ChiPhiKhongHopLeData,
  DieuChinhBoPhanSuDungCCDCInput,
  DotThanhToan,
  HopDong,
  KheUoc,
  KhoHang,
  NhapXuat,
  Phi,
  Tax,
  VatTu,
  VuViec
} from '@/types/schemas';
import { CellField } from '@/components/custom/arito/custom-input-table/components';
import { calculateTienNt } from '@/features/ton-kho/phieu-nhap-kho/utils/calc-util';
import { SearchField } from '@/components/custom/arito';
import { Checkbox } from '@/components/ui/checkbox';

export const getWarehouseReceiptItemColumns = (
  onCellValueChange: (rowUuid: string, field: string, newValue: any) => void
): GridColDef[] => [
  {
    field: 'ma_vt',
    headerName: 'Mã sản phẩm',
    width: 150,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<VatTu>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VAT_TU}`}
        searchColumns={vatTuSearchColumns}
        dialogTitle='Danh mục vật tư'
        columnDisplay='ma_vt'
        value={params.row.ma_vt_data?.ma_vt || ''}
        onRowSelection={(row: any) => {
          onCellValueChange(params.row.uuid, 'ma_vt_data', row);
        }}
      />
    )
  },
  {
    field: 'ten_vt',
    headerName: 'Tên sản phẩm',
    width: 200,
    renderCell: params => (
      <CellField
        name='ten_vt'
        type='text'
        value={params.row.ma_vt_data?.ten_vt || ''}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'ten_vt', newValue)}
      />
    )
  },
  {
    field: 'dvt',
    headerName: 'Đvt',
    width: 100,
    renderCell: params => (
      <CellField
        name='dvt'
        type='select'
        value={params.row.ma_vt_data?.dvt}
        options={[{ value: `${params.row.ma_vt_data?.dvt}`, label: `${params.row.ma_vt_data?.dvt_data?.dvt}` }]}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'dvt', newValue)}
      />
    )
  },
  {
    field: 'ma_kho',
    headerName: 'Mã kho',
    width: 100,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<KhoHang>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHO_HANG}`}
        searchColumns={warehouseSearchColumns}
        dialogTitle='Danh mục kho'
        columnDisplay='ma_kho'
        value={params.row.ma_vt_data?.ma_kho_data?.ma_kho}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_kho_data', row)}
      />
    )
  },
  {
    field: 'so_luong',
    headerName: 'Số lượng',
    width: 100,
    renderCell: (params: GridRenderCellParams) => (
      <CellField
        name='so_luong'
        type='number'
        value={params.row.so_luong || 0.0}
        onValueChange={newValue => {
          onCellValueChange(params.row.uuid, 'so_luong', newValue);
        }}
      />
    )
  },
  {
    field: 'pn_tb',
    headerName: 'Giá trung bình',
    width: 120,
    type: 'boolean',
    renderCell: (params: GridRenderCellParams) => (
      <Checkbox
        name='pn_tb'
        checked={params.row.pn_tb || false}
        onCheckedChange={newValue => onCellValueChange(params.row.uuid, 'pn_tb', newValue)}
      />
    )
  },
  {
    field: 'gia_nt',
    headerName: 'Giá VND',
    width: 100,
    renderCell: (params: GridRenderCellParams) => (
      <CellField
        name='gia_nt'
        type='number'
        value={params.row.gia_nt || 0.0}
        disabled={params.row.pn_tb === true}
        onValueChange={newValue => {
          onCellValueChange(params.row.uuid, 'gia_nt', newValue);
        }}
      />
    )
  },
  {
    field: 'tien_nt',
    headerName: 'Tiền VND',
    width: 100,
    renderCell: params => (
      <CellField
        name='tien_nt'
        type='number'
        value={params.row.tien_nt || calculateTienNt(params.row.gia_nt, params.row.so_luong)}
        onValueChange={newValue => onCellValueChange(params.row.uuid, 'tien_nt', newValue)}
      />
    )
  },
  {
    field: 'tk_vt',
    headerName: 'Tk nợ',
    width: 100,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<AccountModel>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
        searchColumns={accountSearchColumns}
        dialogTitle='Danh mục tài khoản'
        columnDisplay='code'
        value={params.row.tk_vt_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_vt_data', row)}
      />
    )
  },
  {
    field: 'ma_nx',
    headerName: 'Lý do nhập',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<NhapXuat>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.LY_DO_NHAP_XUAT}`}
        searchColumns={lyDoNhapXuatSearchColumns}
        dialogTitle='Lý do nhập xuất'
        columnDisplay='ma_nx'
        value={params.row.ma_nx_data?.ma_nx || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_nx_data', row)}
      />
    )
  },
  {
    field: 'tk_du',
    headerName: 'Tk có',
    width: 100,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<AccountModel>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
        searchColumns={accountSearchColumns}
        dialogTitle='Danh mục tài khoản'
        columnDisplay='code'
        value={params.row.tk_du_data?.code || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'tk_du_data', row)}
      />
    )
  },
  {
    field: 'ma_bp',
    headerName: 'Bộ phận',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<BoPhan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.BO_PHAN}`}
        searchColumns={boPhanSearchColumns}
        dialogTitle='Danh mục bộ phận'
        columnDisplay='ma_bp'
        value={params.row.ma_bp_data?.ma_bp || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_bp_data', row)}
      />
    )
  },
  {
    field: 'ma_vv',
    headerName: 'Vụ việc',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<VuViec>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.VU_VIEC}`}
        searchColumns={vuViecSearchColumns}
        dialogTitle='Danh mục vụ việc'
        columnDisplay='ma_vu_viec'
        value={params.row.ma_vv_data?.ma_vu_viec || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_vv_data', row)}
      />
    )
  },
  {
    field: 'ma_hd',
    headerName: 'Hợp đồng',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<HopDong>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.HOP_DONG}`}
        searchColumns={hopDongSearchColumns}
        dialogTitle='Danh mục hợp đồng'
        columnDisplay='ma_hd'
        value={params.row.ma_hd_data?.ma_hd || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_hd_data', row)}
      />
    )
  },
  {
    field: 'ma_dtt',
    headerName: 'Đợt thanh toán',
    width: 140,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<DotThanhToan>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.DOT_THANH_TOAN}`}
        searchColumns={paymentInstallmentSearchColumns}
        dialogTitle='Danh mục đợt thanh toán'
        columnDisplay='ma_dtt'
        value={params.row.ma_dtt_data?.ma_dtt || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_dtt_data', row)}
      />
    )
  },
  {
    field: 'ma_ku',
    headerName: 'Khế ước',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<KheUoc>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.KHE_UOC}`}
        searchColumns={kheUocSearchColumns}
        dialogTitle='Danh mục khế ước'
        columnDisplay='ma_ku'
        value={params.row.ma_ku_data?.ma_ku || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_ku_data', row)}
      />
    )
  },
  {
    field: 'ma_phi',
    headerName: 'Phí',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<Phi>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.PHI}`}
        searchColumns={phiSearchColumns}
        dialogTitle='Danh mục phí'
        columnDisplay='ma_phi'
        value={params.row.ma_phi_data?.ma_phi || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_phi_data', row)}
      />
    )
  },
  {
    field: 'ma_lsx',
    headerName: 'Lệnh sản xuất',
    width: 140,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<any>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.LENH_SAN_XUAT}`}
        searchColumns={lenhSanXuatSearchColumns}
        dialogTitle='Danh mục lệnh sản xuất'
        columnDisplay='so_lsx'
        value={params.row.ma_lsx_data?.so_lsx || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_lsx_data', row)}
      />
    )
  },
  {
    field: 'ma_cp0',
    headerName: 'C/p không h/lệ',
    width: 140,
    renderCell: (params: GridRenderCellParams) => (
      <SearchField<ChiPhiKhongHopLeData>
        type='text'
        searchEndpoint={`/${QUERY_KEYS.CHI_PHI_KHONG_HOP_LE}/`}
        searchColumns={chiPhiKhongHopLeSearchColumns}
        dialogTitle='Danh mục chi phí không hợp lệ'
        columnDisplay='ma_cp_khl'
        value={params.row.ma_cp0_data?.ma_cpkhl || ''}
        onRowSelection={(row: any) => onCellValueChange(params.row.uuid, 'ma_cp0_data', row)}
      />
    )
  },
  {
    field: 'so_ct_sx1',
    headerName: 'Số lệnh SX',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <CellField name='so_ct_sx1' type='text' value={params.row.so_ct_sx1 || ''} disabled={true} />
    )
  },
  {
    field: 'line_sx1',
    headerName: 'Dòng lệnh SX',
    width: 120,
    renderCell: (params: GridRenderCellParams) => (
      <CellField name='line_sx1' type='text' value={params.row.line_sx1 || ''} disabled={true} />
    )
  }
];
