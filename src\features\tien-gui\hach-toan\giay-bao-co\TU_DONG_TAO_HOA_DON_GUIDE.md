# TU_DONG_TAO_HOA_DON_GUIDE

Hướng dẫn triển khai luồng “tự động tạo chứng từ kế toán từ hóa đơn” theo pattern đã áp dụng cho Giấy báo có (tien-gui/hach-toan/giay-bao-co). Tài liệu này tổng hợp các bướ<PERSON>, vị trí file, và những lưu ý hiệu năng/điều hướng để có thể tái sử dụng cho các module khác.

---

## Mục tiêu luồng

- Tại trang Hóa đơn (nguồn): sau khi Lưu thành công và thỏa điều kiện nghiệp vụ (ví dụ: tạo chứng từ thu qua ngân hàng), điều hướng sang trang chứng từ (đích) kèm query `?hd=<uuid_hoa_don>`.
- Tạ<PERSON> trang <PERSON> từ (đích): nếu có `?hd`, tự động mở form Thêm mới và map dữ liệu từ hóa đơn sang; giữ nguyên `?hd` để nếu reload vẫn tự map lại.
- Nút Lưu/Hủy tại trang Chứng từ (đích): đóng form và quay về trang trước (trang Hóa đơn nguồn).
- Tối ưu tốc độ hiển thị: cache chi tiết hóa đơn ngay sau khi Lưu ở trang nguồn và đọc cache ở trang đích trước khi fallback gọi API.

---

## 1) Tại TRANG NGUỒN (Hóa đơn bán hàng)

File tham khảo: `src/features/ban-hang/hoa-don-ban-ra/hoa-don-ban-hang/index.tsx`

- Sau khi `add` thành công, nếu cần tạo chứng từ, thực hiện:
  1. Gọi API lấy chi tiết hóa đơn theo `uuid` vừa tạo.
  2. Lưu chi tiết vào `sessionStorage` với key chuẩn: `hoa_don_detail_<uuid>`.
  3. `router.push` sang trang đích kèm query `?hd=<uuid>`.

Pseudo (đã hiện diện trong file tham khảo):

```ts
if (formMode === 'add' && createdInvoiceId && data.pt_tao_yn && data.ma_httt === 'CKB') {
  try {
    const detail = await getHoaDonBanHangDetail(createdInvoiceId);
    if (detail) {
      sessionStorage.setItem(`hoa_don_detail_${createdInvoiceId}`, JSON.stringify(detail));
    }
  } catch {}
  router.push(`/tien-gui/hach-toan/giay-bao-co/?hd=${createdInvoiceId}`);
}
```

Lưu ý:

- `getHoaDonBanHangDetail` lấy từ hook nguồn (ví dụ `useHoaDonBanHang`).
- Key cache cần thống nhất: `hoa_don_detail_<uuid>`.

---

## 2) Tại TRANG ĐÍCH – PAGE LIST (Giấy báo có)

File tham khảo: `src/features/tien-gui/hach-toan/giay-bao-co/index.tsx`

- Đọc query `hd` bằng `useSearchParams`.
- Nếu có `hd` và `showForm` đang đóng, tự động mở form Thêm mới. Dùng guard `hasAutoOpenedRef` để tránh mở lặp nhiều lần khi re-render.
- Hành vi sau khi Lưu/Hủy: nếu đang ở flow `?hd`, dùng `router.back()` để quay về trang trước (Hóa đơn).

Pseudo (đã hiện diện trong file tham khảo):

```ts
// Auto-open Add form khi có ?hd
useEffect(() => {
  if (hoaDonId && !showForm && !hasAutoOpenedRef.current) {
    hasAutoOpenedRef.current = true;
    handleAddClick();
  }
}, [hoaDonId, showForm, handleAddClick]);

// Sau khi submit thành công
await addGiayBaoCo(data);
handleCloseForm();
clearSelection();
if (hoaDonId) router.back();

// Khi Đóng/Hủy
handleCloseForm();
clearSelection();
if (hoaDonId) router.back();
```

Lưu ý:

- Giữ `?hd` trên URL, không xóa query, để nếu reload vẫn tự mở form và map dữ liệu.

---

## 3) Tại TRANG ĐÍCH – ADD FORM (Giấy báo có)

File tham khảo: `src/features/tien-gui/hach-toan/giay-bao-co/components/add-form/index.tsx`

- Đọc `hd` từ URL.
- Khi `formMode === 'add'` và chưa load hóa đơn:
  1. Thử đọc cache `sessionStorage.getItem(hoa_don_detail_<uuid>)`.
  2. Nếu không có cache, gọi `getHoaDonBanHangDetail(uuid)`.
  3. Map dữ liệu hóa đơn → dữ liệu form chứng từ bằng util `mapHoaDonToGiayBaoCo`.
  4. Đổ vào `detailRows` và đánh dấu đã load (`hasLoadedHoaDon = true`).

Pseudo (đã hiện diện trong file tham khảo):

```ts
const cacheKey = `hoa_don_detail_${hoaDonId}`;
let hoaDonDetail = sessionStorage.getItem(cacheKey) ? JSON.parse(sessionStorage.getItem(cacheKey)!) : null;
if (!hoaDonDetail) {
  hoaDonDetail = await getHoaDonBanHangDetail(hoaDonId);
}
const mapped = mapHoaDonToGiayBaoCo(hoaDonDetail, hoaDonDetail?.chi_tiet || []);
setDetailRows(mapped.chi_tiet);
setHasLoadedHoaDon(true);
```

### (Tuỳ chọn) Prefill tài khoản theo prefix

- Không hard-code tài khoản mặc định.
- Dùng `useMemo` tạo prefix (ví dụ `'1111'`).
- Dùng `useCRUD` với `endpoint: QUERY_KEYS.TAI_KHOAN` và `searchData: { code__startswith: prefix }` để lấy danh sách tài khoản.
- Nếu `state.taiKhoan` đang chưa có, lấy phần tử đầu tiên trong kết quả để `actions.setTaiKhoan(...)`.
- Đây là tuỳ chọn: module khác có thể bỏ qua hoặc đổi prefix/tiêu chí chọn.

Pseudo (đã hiện diện trong file tham khảo):

```ts
const defaultAccountPrefix = useMemo(() => '1111', []);
const { data: defaultAccounts } = useCRUD({
  endpoint: QUERY_KEYS.TAI_KHOAN,
  searchData: { code__startswith: defaultAccountPrefix },
  pageSize: 10
});

useEffect(() => {
  if (formMode !== 'add') return;
  if (!state.taiKhoan && defaultAccounts && defaultAccounts.length > 0) {
    const first: any = defaultAccounts[0];
    if (first?.uuid && (first?.code || first?.name)) {
      actions.setTaiKhoan({ uuid: first.uuid, code: first.code, name: first.name } as any);
    }
  }
}, [formMode, state.taiKhoan, defaultAccounts, actions]);
```

---

## 4) Utility MAP dữ liệu

File tham khảo: `src/features/tien-gui/hach-toan/giay-bao-co/utils/mapHoaDonData.ts`

- Trả về object gồm `chi_tiet` đã quy đổi các field cần thiết cho chứng từ.
- Các điểm đáng chú ý đã được chuẩn hóa cho transform ở chứng từ:
  - `id_hd_data.ID` (uuid hóa đơn)
  - `id_hd_data.tk_data.uuid` và `tk` (nếu có từ hóa đơn)
  - `ty_gia`, `tien_tren_hd`, `tien_hd_nt`, `tien_con_phai_tt`
  - Các `ma_*_data` giữ nguyên cấu trúc để downstream đọc `.uuid` khi cần
- Không còn hard-code `formFields.taiKhoan` trong mapper (phần tài khoản mặc định đã chuyển sang tuỳ chọn ở Add Form).

---

## 5) Điều hướng sau Lưu/Hủy

- Nếu đang trong flow từ hóa đơn (có `?hd`): `router.back()` để quay lại trang trước.
- Nếu không có `?hd`: giữ behavior mặc định (đóng form và ở lại list page).

---

## 6) Hiệu năng & UX

- Cache chi tiết hóa đơn tại trang nguồn ngay khi Lưu xong giúp trang đích hiển thị tức thì.
- Trang đích ưu tiên đọc cache, chỉ gọi API khi không có cache.
- Giữ `?hd` để người dùng `reload` vẫn ở đúng flow và dữ liệu được nạp lại tự động.
- Dùng guard `hasAutoOpenedRef` để tránh auto-open form nhiều lần trong một lần tải trang.

---

## 7) Checklist áp dụng cho module khác

1. Tại trang nguồn (ví dụ Hóa đơn X):
   - Sau Lưu thành công, nếu cần tạo chứng từ, `sessionStorage.setItem('hoa_don_detail_<uuid>', JSON.stringify(detail))`.
   - `router.push('<duong_dan_chung_tu>/?hd=<uuid>')`.
2. Tại page list của chứng từ:
   - Đọc `hd` và auto-open form Thêm mới một lần (dùng ref guard).
   - Khi Lưu/Hủy nếu có `hd`: `router.back()`.
3. Tại Add Form chứng từ:
   - Đọc cache chi tiết hóa đơn; nếu không có thì gọi API detail.
   - Map dữ liệu qua util `map...` và đổ vào rows/state.
   - (Tuỳ chọn) Prefill tài khoản bằng `useCRUD + code__startswith`.
4. Tối ưu/tinh chỉnh theo nghiệp vụ cụ thể (điều kiện tạo chứng từ, prefix TK, mapping fields bổ sung,...).

---

## 8) Lưu ý & Edge cases

- Cache hỏng/không hợp lệ: luôn có fallback gọi API chi tiết.
- Tránh stale cache: có thể `removeItem` sau khi đọc tuỳ theo yêu cầu (đã làm ở Giấy báo có); hoặc giữ để hỗ trợ quay lại.
- Nếu `router.back()` không có lịch sử (vào trực tiếp với `?hd`): cân nhắc fallback `router.replace(<link_hoa_don_nguon>)`.
- Kiểm tra quyền truy cập (entity slug) trước khi gọi API.

---

## 9) Về tài liệu cũ

- Có thể xoá file `IMPLEMENT_GUIDE` nếu không còn dùng (giữ tài liệu này làm chuẩn mới cho pattern tự động).

---

## 10) Kiểm thử đề xuất

- Unit test util map: đầu vào mẫu hóa đơn → đầu ra `chi_tiet` đúng field.
- E2E (giản lược):
  1. Lưu hóa đơn với điều kiện tạo chứng từ → điều hướng sang trang chứng từ với `?hd`.
  2. Xác nhận form auto-open, dữ liệu chi tiết hiển thị ngay (cache), không có cache thì vẫn hiển thị (API).
  3. Reload trang → form vẫn auto-open và map lại.
  4. Nhấn Lưu/Hủy → quay về trang hóa đơn nguồn.
