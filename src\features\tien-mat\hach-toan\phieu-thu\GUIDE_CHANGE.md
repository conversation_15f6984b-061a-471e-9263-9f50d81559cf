## GUIDE_CHANGE: <PERSON>ẩn hoá luồng MultipleForms và phân trang server-side (áp dụng từ Phiếu thu)

Mục tiêu: Tài liệu hoá các thay đổi chính đã làm cho Phiếu thu để có thể áp dụng tương tự cho các chứng từ khác (vd: <PERSON><PERSON><PERSON> chi, <PERSON><PERSON><PERSON><PERSON> báo có/đỏ, v.v.).

Các file chính:
- hooks/useMultipleForms.ts
- utils/transform-data.ts
- index.tsx (trang Phiếu thu)

Ngoài ra có 2 nền tảng liên quan:
- useCRUD: hỗ trợ initialFetch để tắt fetch ban đầu khi vào flow MultipleForms
- api interceptor (src/lib/api.ts): chỉ hiện toast thành công với các method POST/PUT/DELETE

---

### 1) hooks/useMultipleForms.ts

Chức năng:
- <PERSON><PERSON><PERSON> hiện params URL (hdbh/hdbdv) để kích hoạt flow MultipleForms
- Tải dữ liệu các hoá đơn theo UUID
- Quản lý state MultipleForms: showMultipleForms, invoiceData, currentFormIndex
- Dựng initialData (từ transformDataFromHoaDon) + tiêm tk_data mặc định (prefix 1111)
- Submit từng form: tạo chứng từ, chuyển sang form kế tiếp; nếu là form cuối -> router.back()

Cấu trúc API hook:
- Input: { addPhieuThu }
- Return:
  - hasInvoiceParams: boolean
  - showMultipleForms: boolean
  - invoiceData: any[]
  - currentFormIndex: number
  - initialData: dữ liệu khởi tạo form hiện tại
  - handleMultipleFormSubmit(data)
  - handleCloseMultipleForms()

Điểm lưu ý khi áp dụng cho chứng từ khác:
- Điều chỉnh tham số URL cần nhận (vd: "hdmv", "hdbh", …) cho đúng ngữ cảnh module
- Điều chỉnh endpoint fetch hoá đơn (QUERY_KEYS) phù hợp (vd: HOA_DON_BAN_HANG, HOA_DON_BAN_DICH_VU, HOA_DON_MUA_VAO,…)
- Sử dụng useCRUD để lấy danh mục Tài khoản (prefix 1111) nếu cũng muốn gán tk_data mặc định
- Không cần gọi toast thành công trong hook; interceptor đã lo ở mức API đối với POST/PUT/DELETE

Checklist chuyển đổi cho module khác:
1. Tạo hooks/useMultipleForms.ts (copy/điều chỉnh từ Phiếu thu)
2. Đổi danh sách params URL đặc thù của module
3. Đổi endpoint fetch hoá đơn
4. Ký hiệu type form input phù hợp (PhieuChiInput, GiayBaoCoInput, …)
5. Trả về và sử dụng initialData theo transform của module đó

---

### 2) utils/transform-data.ts

Mục tiêu:
- Biến dữ liệu hóa đơn -> initialData cho form tạo nhanh
- Cung cấp transformDetailRows/transformBaseData/transformFormData nếu cần gửi payload đầy đủ
- Riêng initialData cho MultipleForms dùng transformDataFromHoaDon(hoaDonData)

Những trường quan trọng trong Phiếu thu:
- baseData: ma_ngv, dia_chi, ong_ba, dien_giai, ma_nt, ty_gia, status, transfer_yn, ngay_ct0, so_ct, tk_data (được gán ở hook sau khi tải danh mục TK)
- child_data: mảng chi tiết từ chi_tiet hóa đơn, gồm các trường tham chiếu (ma_kh_data, id_hd_data, số tiền, bộ phận, vụ việc, …)

Khi áp dụng cho module khác:
- Tạo file utils/transform-data.ts tương ứng
- Viết transformDataFromHoaDon(hoaDonData) phù hợp với form đầu vào của module đó
- Đảm bảo các trường "_data" khớp với SearchField/columns của module

Checklist:
1. Xác định các field baseData cần điền mặc định
2. Xác định child_data và mapping từ hoá đơn
3. Kiểm tra loại tiền, tỷ giá, trạng thái mặc định
4. Test initialData hiển thị đúng ở form

---

### 3) index.tsx (Phiếu thu)

Các thay đổi chính:
- Phân trang server-side: chuyển sang dùng useCRUD thay vì useDataTables
  - Lấy data, totalItems, currentPage, handlePageChange, isLoading
  - Build tables = [{ name: 'Tất cả', rows: data, columns: getXXXColumns() }]
  - Truyền totalItems/currentPage/onPageChange/serverSidePagination vào AritoDataTables
- Bật flow MultipleForms khi phát hiện params URL:
  - hasInvoiceParams = useInvoiceParamsPresence()
  - useCRUD({ initialFetch: !hasInvoiceParams }) để không fetch danh sách khi đang ở flow MultipleForms
  - Gọi useMultipleForms({ addItemFn }) để render PhieuThuForm thay cho trang chính

Mẫu tích hợp rút gọn:
- Dùng useCRUD với endpoint tương ứng
- Dùng useInvoiceParamsPresence() để xác định flow
- Dùng useMultipleForms() để hiển thị form khi showMultipleForms == true
- Khi không ở flow MultipleForms -> render trang chính với AritoDataTables

Checklist áp dụng cho module khác:
1. Import useCRUD, QUERY_KEYS.<MODULE_ENDPOINT>
2. Tạo tables dựa trên data từ useCRUD và getColumns của module
3. Truyền full props phân trang vào AritoDataTables:
   - totalItems={totalItems}
   - currentPage={currentPage}
   - onPageChange={handlePageChange}
   - serverSidePagination
4. Thêm useInvoiceParamsPresence (hoặc biến thể) cho params của module
5. Gọi useCRUD với initialFetch: !hasInvoiceParams
6. Gọi useMultipleForms({ add<Module> }) để render form MultipleForms

---

### 4) Interceptor toast thành công chỉ cho POST/PUT/DELETE

- src/lib/api.ts: interceptor response chỉ toast khi method là post/put/delete
- Ưu điểm: Không cần gọi toast thủ công khi submit từng form; tự động đồng nhất thông báo thành công ở toàn app
- Nếu server trả về data.message (string), client sẽ hiển thị nội dung đó; nếu không, fallback: "Cập nhật thành công"

Checklist áp dụng chung:
- Không thêm toast thủ công trong hook MultipleForms
- Nếu cần nội dung cụ thể hơn, cân nhắc cho BE trả về message phù hợp theo ngữ cảnh

---

### 5) Gợi ý tái sử dụng cho các chứng từ khác

- Tạo bộ ba file tương tự:
  - hooks/useMultipleForms.ts: đọc params, fetch chứng từ nguồn, quản lý multiple-state, submit/back
  - utils/transform-data.ts: map dữ liệu nguồn -> initialData form của module
  - index.tsx: dùng useCRUD + AritoDataTables (server-side), tích hợp MultipleForms

- Đổi các hằng số/loại sau cho phù hợp:
  - QUERY_KEYS endpoint chính, endpoint hoá đơn nguồn
  - Kiểu dữ liệu Input/Entity (PhieuChiInput, GiayBaoCoInput, …)
  - Columns (get<Module>Columns)
  - Params URL đặc thù (vd: hdbh/hdbdv -> module khác có thể khác)

- Kiểm thử tối thiểu:
  - Truy cập trang với params -> hiển thị form từ hoá đơn, không fetch list
  - Submit từng form -> method POST -> interceptor toast thành công
  - Submit form cuối -> router.back()
  - Truy cập trang không params -> bảng + phân trang server-side hoạt động bình thường

---

### 6) Mẹo & cảnh báo
- initialFetch trong useCRUD nên được set false khi ở flow MultipleForms để tránh gọi API không cần thiết
- Hãy chắc chắn transformDataFromHoaDon trả về đủ field để form không lỗi (đặc biệt các trường _data cho SearchField)
- Nếu cần lọc theo trạng thái/bổ sung tab, có thể build thêm nhiều tabs trong tables (vẫn dựa theo data từ useCRUD)

Hoàn tất.

