import { <PERSON>er, Plus, Pencil, Trash, Copy, LogOut, RefreshCw, Table, Save } from 'lucide-react';
import { useMemo, useState, useEffect, useCallback } from 'react';
import { useWatch } from 'react-hook-form';
import { BasicInfoTab, BottomBar, DetailItemTab, ExchangeRateTab, OtherTab, PaymentInfoTab, BankFeeTab } from './tabs';
import { AritoForm, AritoHeaderTabs, AritoActionButton } from '@/components/custom/arito';
import { exportCreditAdviceSchema, initialFormValues } from '../../schemas';
import { useBankFeeRows, useDetailItemRows } from './tabs/hooks';
import type { AccountModel } from '@/types/schemas';

import { useAuth } from '@/contexts/auth-context';
import QUERY_KEYS from '@/constants/query-keys';
import { useCRUD } from '@/hooks';

import { useSearchParams } from 'next/navigation';
import { mapHoaDonToGiayBaoCo } from '../../utils/mapHoaDonData';
import { calculatePaymentTotals } from '../../utils/calc-utils';
import { transformFormData } from '../../utils';
import { useFormFieldState } from '../../hooks';
import { useToast } from '@/hooks/use-toast';
import { useHoaDonBanHang } from '@/hooks';
import { HistoryTab } from './HistoryTab';
import { FormMode } from '@/types/form';

const DetailItemTabWrapper = ({
  formMode,
  detailRows,
  detailSelectedRowUuid,
  detailHandleRowClick,
  detailHandleAddRow,
  detailHandleDeleteRow,
  detailHandleCopyRow,
  detailHandlePasteRow,
  detailHandleMoveRow,
  detailHandleCellValueChange
}: any) => {
  const [ma_ngv, dien_giai] = useWatch({ name: ['ma_ngv', 'dien_giai'] });

  return (
    <DetailItemTab
      formMode={formMode}
      rows={detailRows}
      selectedRowUuid={detailSelectedRowUuid}
      onRowClick={detailHandleRowClick}
      onAddRow={detailHandleAddRow}
      onDeleteRow={detailHandleDeleteRow}
      onCopyRow={detailHandleCopyRow}
      onPasteRow={detailHandlePasteRow}
      onMoveRow={detailHandleMoveRow}
      onCellValueChange={detailHandleCellValueChange}
      ma_ngv={ma_ngv || '1'}
      dien_giai={dien_giai}
    />
  );
};

export const AddForm = ({
  formMode,
  initialData,
  onSubmit,
  onClose
}: {
  formMode: FormMode;
  initialData: any;
  onSubmit: (data: any) => void;
  onClose: () => void;
}) => {
  const { toast } = useToast();

  const searchParams = useSearchParams();
  const hoaDonId = searchParams.get('hd');

  const { getHoaDonBanHangDetail } = useHoaDonBanHang();
  const { entity } = useAuth();

  const [isLoadingHoaDon, setIsLoadingHoaDon] = useState(false);
  const [hasLoadedHoaDon, setHasLoadedHoaDon] = useState(false);

  // Prepare default account search by prefix via useCRUD
  const defaultAccountPrefix = useMemo(() => '1111', []);
  const { data: defaultAccounts } = useCRUD({
    endpoint: QUERY_KEYS.TAI_KHOAN,
    searchData: { code__startswith: defaultAccountPrefix },
    pageSize: 10
  });

  const [activeTab, setActiveTab] = useState('info');

  const {
    rows: detailRows,
    setRows: setDetailRows,
    selectedRowUuid: detailSelectedRowUuid,
    handleRowClick: detailHandleRowClick,
    handleAddRow: detailHandleAddRow,
    handleDeleteRow: detailHandleDeleteRow,
    handleCopyRow: detailHandleCopyRow,
    handlePasteRow: detailHandlePasteRow,
    handleMoveRow: detailHandleMoveRow,
    handleCellValueChange: detailHandleCellValueChange
  } = useDetailItemRows(initialData?.chi_tiet_data || []);
  const {
    rows: bankFeeRows,
    selectedRowUuid: bankFeeSelectedRowUuid,
    handleRowClick: bankFeeHandleRowClick,
    handleAddRow: bankFeeHandleAddRow,
    handleDeleteRow: bankFeeHandleDeleteRow,
    handleCopyRow: bankFeeHandleCopyRow,
    handlePasteRow: bankFeeHandlePasteRow,
    handleMoveRow: bankFeeHandleMoveRow,
    handleCellValueChange: bankFeeHandleCellValueChange
  } = useBankFeeRows(initialData?.chi_tiet_phi_data || []);
  const { state, actions } = useFormFieldState(initialData);
  const { entityUnit } = useAuth();

  useEffect(() => {
    const fetchHoaDonData = async () => {
      if (hoaDonId && formMode === 'add' && !hasLoadedHoaDon) {
        setIsLoadingHoaDon(true);
        try {
          const cacheKey = `hoa_don_detail_${hoaDonId}`;
          let hoaDonDetail: any = null;
          if (typeof window !== 'undefined') {
            const cached = sessionStorage.getItem(cacheKey);
            if (cached) {
              try {
                hoaDonDetail = JSON.parse(cached);
                sessionStorage.removeItem(cacheKey); // avoid stale data
              } catch (e) {
                // ignore JSON parse errors
              }
            }
          }

          if (!hoaDonDetail) {
            hoaDonDetail = await getHoaDonBanHangDetail(hoaDonId);
          }

          const hoaDonInfo = hoaDonDetail;
          const chiTietList = (hoaDonDetail as any)?.chi_tiet || [];
          const mappedData = mapHoaDonToGiayBaoCo(hoaDonInfo, chiTietList);

          setDetailRows(mappedData.chi_tiet);

          setHasLoadedHoaDon(true);
        } catch (error) {
          //TODO
        } finally {
          setIsLoadingHoaDon(false);
        }
      }
    };

    fetchHoaDonData();
  }, [hoaDonId, formMode, hasLoadedHoaDon, getHoaDonBanHangDetail, actions, setDetailRows, toast]);

  const { tong_tien, tong_thanh_toan, tong_thue } = useMemo(() => {
    return calculatePaymentTotals(detailRows, bankFeeRows);
  }, [detailRows, bankFeeRows]);

  const handleSubmit = (data: any) => {
    const formData = transformFormData(data, state, detailRows, bankFeeRows, entityUnit);
    onSubmit(formData);
  };

  // Set default account by prefix if not present
  useEffect(() => {
    if (formMode !== 'add') return;
    if (!state.taiKhoan && defaultAccounts && defaultAccounts.length > 0) {
      const first: any = defaultAccounts[0];
      if (first?.uuid && (first?.code || first?.name)) {
        actions.setTaiKhoan({ uuid: first.uuid, code: first.code, name: first.name } as any);
      }
    }
  }, [formMode, state.taiKhoan, defaultAccounts, actions]);

  const getTitle = () => {
    if (formMode !== 'view') {
      return formMode === 'add' ? 'Mới' : 'Sửa';
    }

    switch (activeTab) {
      case 'history':
        return 'Lịch sử';
      default:
        return 'Giấy báo có';
    }
  };

  const getActionButtons = () => {
    if (formMode !== 'view') {
      return (
        <>
          <AritoActionButton title='Lưu' icon={Save} variant='secondary' type='submit' />
          <AritoActionButton title='Đóng' icon={LogOut} variant='destructive' onClick={onClose} />
        </>
      );
    }

    switch (activeTab) {
      case 'history':
        return (
          <>
            <AritoActionButton title='Refresh' icon={RefreshCw} variant='secondary' onClick={() => {}} />
            <AritoActionButton title='Cố định cột' icon={Table} variant='secondary' onClick={() => {}} />
            <AritoActionButton title='Đóng' icon={LogOut} variant='destructive' onClick={onClose} />
          </>
        );
      default:
        return (
          <>
            <AritoActionButton title='In' icon={Printer} variant='secondary' onClick={() => {}} />
            <AritoActionButton title='Thêm' icon={Plus} variant='primary' onClick={() => {}} />
            <AritoActionButton title='Sửa' icon={Pencil} variant='secondary' onClick={() => {}} />
            <AritoActionButton title='Xóa' icon={Trash} variant='destructive' onClick={() => {}} />
            <AritoActionButton title='Sao chép' icon={Copy} variant='secondary' onClick={() => {}} />
            <AritoActionButton title='Đóng' icon={LogOut} variant='destructive' onClick={onClose} />
          </>
        );
    }
  };

  if (initialData && formMode === 'add') {
    initialData = {
      ...initialData,
      ngay_ct: new Date().toISOString().split('T')[0],
      ngay_lct: new Date().toISOString().split('T')[0]
    };
  }

  return (
    <div className='h-full flex-1 overflow-auto'>
      {isLoadingHoaDon && (
        <div className='absolute inset-0 z-50 flex items-center justify-center bg-white bg-opacity-75'>
          <div className='text-center'>
            <div className='mx-auto mb-2 h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600'></div>
            <p className='text-sm text-gray-600'>Đang tải thông tin hóa đơn...</p>
          </div>
        </div>
      )}
      <AritoForm
        mode={formMode}
        hasAritoActionBar={true}
        initialData={initialData || initialFormValues}
        onSubmit={handleSubmit}
        onClose={onClose}
        schema={exportCreditAdviceSchema}
        title={getTitle()}
        subTitle='Giấy báo có'
        headerFields={
          <AritoHeaderTabs
            defaultTabIndex={0}
            onTabChange={tabId => setActiveTab(tabId)}
            tabs={[
              {
                id: 'info',
                label: 'Thông tin',
                component: <BasicInfoTab formMode={formMode} formState={{ state, actions }} />
              },
              ...(formMode !== 'add'
                ? [{ id: 'history', label: 'Lịch sử', component: <HistoryTab formMode={formMode} /> }]
                : [])
            ]}
          />
        }
        tabs={
          activeTab === 'info'
            ? [
                {
                  id: 'details',
                  label: 'Chi tiết',
                  component: (
                    <DetailItemTabWrapper
                      formMode={formMode}
                      detailRows={detailRows}
                      detailSelectedRowUuid={detailSelectedRowUuid}
                      detailHandleRowClick={detailHandleRowClick}
                      detailHandleAddRow={detailHandleAddRow}
                      detailHandleDeleteRow={detailHandleDeleteRow}
                      detailHandleCopyRow={detailHandleCopyRow}
                      detailHandlePasteRow={detailHandlePasteRow}
                      detailHandleMoveRow={detailHandleMoveRow}
                      detailHandleCellValueChange={detailHandleCellValueChange}
                    />
                  )
                },
                {
                  id: 'paymentInfo',
                  label: 'Thông tin thanh toán',
                  component: <PaymentInfoTab formMode={formMode} formState={{ state, actions }} />
                },
                {
                  id: 'exchangeRate',
                  label: 'Tỷ giá',
                  component: <ExchangeRateTab formMode={formMode} />
                },
                {
                  id: 'bankFee',
                  label: 'Phí ngân hàng',
                  component: (
                    <BankFeeTab
                      formMode={formMode}
                      rows={bankFeeRows}
                      selectedRowUuid={bankFeeSelectedRowUuid}
                      onRowClick={bankFeeHandleRowClick}
                      onAddRow={bankFeeHandleAddRow}
                      onDeleteRow={bankFeeHandleDeleteRow}
                      onCopyRow={bankFeeHandleCopyRow}
                      onPasteRow={bankFeeHandlePasteRow}
                      onMoveRow={bankFeeHandleMoveRow}
                      onCellValueChange={bankFeeHandleCellValueChange}
                    />
                  )
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab formMode={formMode} />
                }
              ]
            : []
        }
        actionButtons={getActionButtons()}
        bottomBar={
          <BottomBar tong_tien={tong_tien} tong_thanh_toan={tong_thanh_toan} tong_thue={tong_thue} state={state} />
        }
      />
    </div>
  );
};
